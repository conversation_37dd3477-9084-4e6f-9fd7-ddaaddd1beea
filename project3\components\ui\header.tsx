'use client';

import {
  Phone,
  Mail,
  Clock,
  MessageCircle,
  User,
  UserPlus,
  Heart,
  ShoppingCart,
  Menu,
  Search,
  ChevronDown,
  Sun,
  Moon,
  Globe,
  ChevronRight,
  X,
  Package,
  LogOut,
  MapPin
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { toast } from 'sonner';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { useSettings } from '@/contexts/settings-context';
import { useUser } from '@/contexts/user-context';
import { ColorPicker } from './color-picker';

type Category = {
  id: number;
  name: string;
  subcategories: string[];
};

export function Header() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showMobileCategories, setShowMobileCategories] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [cartCount, setCartCount] = useState(0);
  const [wishlistCount, setWishlistCount] = useState(0);
  const cart = useCart();
  const wishlist = useWishlist();
  const { user, isLoggedIn, logout } = useUser();
  const { theme, language, primaryColor, primaryTextColor, toggleTheme, setLanguage, setPrimaryColor, t } = useSettings();

  const handleSearch = () => {
    // Navigate to products page with search parameters
    const params = new URLSearchParams();

    if (searchTerm) {
      params.append('search', searchTerm);
    }

    if (selectedCategoryId) {
      params.append('category', selectedCategoryId.toString());
    }

    router.push(`/products?${params.toString()}`);
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const param = {
          "PageNumber": 1,
          "PageSize": 100,
          "SortColumn": "Name",
          "SortOrder": "ASC"
        };
        const headers = {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer ' + localStorage.getItem('token')
        };
        const categoriesResponse = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_CATEGORIES_LIST, null, param, headers, "POST", true);

        if (categoriesResponse?.data?.data) {
          try {
            const parsedData = JSON.parse(categoriesResponse.data.data);
            if (Array.isArray(parsedData)) {
              // Create a map of parent categories
              const parentCategories = parsedData.filter(cat => !cat.ParentCategoryID);
              const childCategories = parsedData.filter(cat => cat.ParentCategoryID);

              // Format parent categories with their children
              const formattedCategories = parentCategories.map(parent => ({
                id: parent.CategoryID,
                name: parent.Name,
                subcategories: childCategories
                  .filter(child => child.ParentCategoryID === parent.CategoryID)
                  .map(child => child.Name)
              }));

              setCategories(formattedCategories);
            } else {
              console.error('Categories data is not an array:', parsedData);
              setCategories([]);
            }
          } catch (parseError) {
            console.error('Error parsing categories data:', parseError);
            setCategories([]);
          }
        } else if (categoriesResponse?.data?.errorMessage) {
          console.error('API Error:', categoriesResponse.data.errorMessage);
          setCategories([]);
        } else {
          console.error('Invalid or empty response from API');
          setCategories([]);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setCategories([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Update cart count when cart items change
  useEffect(() => {
    if (cart && cart.items) {
      setCartCount(cart.totalItems);
    }
  }, [cart, cart?.items]);

  // Update wishlist count when wishlist items change
  useEffect(() => {
    if (wishlist) {
      setWishlistCount(wishlist.totalItems);
    }
  }, [wishlist, wishlist?.wishlistItems]);

  return (
    <header className="w-full">
      <Button
        variant="ghost"
        size="sm"
        className="fixed right-0 top-1/2 -translate-y-1/2 z-50 bg-gradient-to-r from-background/90 to-background/60 backdrop-blur-sm shadow-lg rounded-l-lg border-l border-y border-border/40 md:flex hidden hover:translate-x-1 transition-all duration-200 hover:shadow-xl group items-center justify-center w-16 h-12"
        onClick={() => setShowColorPicker(true)}
      >
        <div className="h-8 w-8 rounded-full ring-2 ring-border/50 group-hover:ring-primary/50 transition-all duration-200 shadow-inner" style={{ backgroundColor: primaryColor }} />
      </Button>

      {/* Top Bar - Desktop Only */}
      <div className="hidden md:block py-2.5" style={{ backgroundColor: primaryColor, color: primaryTextColor }}>
        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4">
          <div className="flex md:flex-row items-start justify-start gap-4 md:gap-8">
            <Link href="tel:009647836071686" className="flex items-center gap-2 hover:text-white/80">
              <Phone className="h-4 w-4" />
              <span className="text-xs md:text-sm">{t('phone')}</span>
            </Link>
            <Link href="mailto:<EMAIL>" className="flex items-center gap-2 hover:text-white/80">
              <Mail className="h-4 w-4" />
              <span className="text-xs md:text-sm">{t('email')}</span>
            </Link>
          </div>
          <div className="flex items-center gap-2 md:gap-4">
            <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2" onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}>
              <Globe className="h-4 w-4" />
              <span className="text-sm">{language === 'en' ? 'العربية' : 'English'}</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:text-white/80 flex items-center gap-2"
              onClick={() => window.open(`https://wa.me/9647836071686?text=${encodeURIComponent('Hello! I would like to chat with you regarding your services.')}`, '_blank')}
            >
              <MessageCircle className="h-4 w-4" />
              <span className="text-sm">{t('liveChat')}</span>
            </Button>
            {isLoggedIn ? (
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="text-sm">Welcome, {user?.FirstName || user?.UserName}</span>
                    <ChevronDown className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2 bg-white border border-gray-200 shadow-lg" align="end">
                  <div className="space-y-1">
                    <Link href="/account">
                      <Button variant="ghost" size="sm" className="w-full justify-start hover:bg-gray-50">
                        <User className="h-4 w-4 mr-2" />
                        My Account
                      </Button>
                    </Link>
                    <Link href="/orders">
                      <Button variant="ghost" size="sm" className="w-full justify-start hover:bg-gray-50">
                        <Package className="h-4 w-4 mr-2" />
                        My Orders
                      </Button>
                    </Link>
                    <Link href="/addresses">
                      <Button variant="ghost" size="sm" className="w-full justify-start hover:bg-gray-50">
                        <MapPin className="h-4 w-4 mr-2" />
                        My Addresses
                      </Button>
                    </Link>
                    <Link href="/wishlist">
                      <Button variant="ghost" size="sm" className="w-full justify-start hover:bg-gray-50">
                        <Heart className="h-4 w-4 mr-2" />
                        Wishlist
                      </Button>
                    </Link>
                    <div className="border-t border-gray-100 my-1"></div>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                      onClick={() => {
                        logout();
                        router.push('/');
                        toast.success('Logged out successfully');
                      }}
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      Logout
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            ) : (
              <>
                <Link href="/login">
                  <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span className="text-sm">{t('login')}</span>
                  </Button>
                </Link>
                <Link href="/signup">
                  <Button variant="ghost" size="sm" className="text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    <span className="text-sm">{t('signUp')}</span>
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto py-4 px-4">
        {/* Mobile Layout - Logo and Search on separate lines */}
        <div className="md:hidden">
          {/* Logo Row with Language Selector */}
          <div className="flex items-center justify-between mb-4">
            {/* Left spacer for perfect centering */}
            <div className="w-20"></div>

            {/* Centered Logo */}
            <Link href="/" className="flex items-center gap-2">
              <div className="text-[#1B3764] flex items-center gap-2">
                <img src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} alt="Logo" className="h-12 w-auto" />
              </div>
            </Link>

            {/* Language Selector with Flag */}
            <Button
              variant="ghost"
              size="sm"
              className="flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50"
              onClick={() => setLanguage(language === 'en' ? 'ar' : 'en')}
            >
              <span className="text-lg">
                {language === 'en' ? '🇺🇸' : '🇮🇶'}
              </span>
              <span className="text-sm font-medium">
                {language === 'en' ? 'EN' : 'AR'}
              </span>
            </Button>
          </div>

          {/* Search Row - No category dropdown */}
          <div className="w-full">
            <div className="flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm">
              <input
                type="text"
                placeholder={t('products') || 'البحث عن المنتجات...'}
                className="bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 hover:bg-accent/80 transition-colors"
                style={{ color: primaryColor }}
                onClick={handleSearch}
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        {/* Desktop Layout */}
        <div className="hidden md:flex items-center justify-between">
          {/* Logo and Search */}
          <div className="flex items-center gap-4 flex-1">
            {/* Logo */}
            <Link href="/" className="flex items-center gap-2">
              <div className="text-[#1B3764] flex items-center gap-2">
                <img src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`} alt="Logo" className="h-16 w-auto" />
              </div>
            </Link>

            {/* Search and Category - Moved next to logo */}
            <div className="flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" className="h-8 flex items-center gap-1 px-2">
                    <span className="text-muted-foreground text-sm">{selectedCategory || selectedSubcategory || t('category')}</span>
                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-0 bg-white border border-gray-200 shadow-lg" align="start">
                  <div className="max-h-[300px] overflow-auto">
                    {isLoading ? (
                      <div className="p-4 text-center text-muted-foreground">{t('loadingCategories')}</div>
                    ) : (
                      <div className="grid">
                        {categories.map((category) => (
                          <div key={category.id} className="group">
                            <button className="w-full px-4 py-2 text-left hover:bg-gray-50" onClick={() => {
                              setSelectedCategory(category.name);
                              setSelectedCategoryId(category.id);
                              setSelectedSubcategory(null);
                            }}>
                              {category.name}
                            </button>
                            <div className="hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border border-gray-200">
                              {category.subcategories.map((sub, index) => (
                                <button
                                  key={index}
                                  className="w-full px-4 py-2 text-left hover:bg-gray-50"
                                  onClick={() => {
                                    setSelectedSubcategory(sub);
                                    setSelectedCategory(null);
                                    // Keep the parent category ID for search purposes
                                    setSelectedCategoryId(category.id);
                                  }}
                                >
                                  {sub}
                                </button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
              <div className="h-5 w-px bg-border mx-2" />
              <input
                type="text"
                placeholder={t('products')}
                className="bg-transparent border-none focus:outline-none text-sm flex-1"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button variant="ghost" className="h-8 w-8 p-0" onClick={handleSearch}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Navigation - Desktop */}
          <div className="hidden md:block">
            <NavigationMenu>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('home')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/hot-deals" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('hotDeals')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/products" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('products') || 'Products'}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/payment-methods" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('paymentMethods')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/follow-us" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('followUs')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/about" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('aboutUs')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link href="/contact" className={cn(
                      "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"
                    )}>
                      {t('contactUs')}
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Cart and Wishlist Icons - Desktop Only */}
          <div className="hidden md:flex items-center gap-4">
            <Link href="/wishlist">
              <Button variant="ghost" size="icon" className="relative">
                <Heart className="h-5 w-5" style={{ color: primaryColor }} />
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  {wishlistCount}
                </span>
              </Button>
            </Link>
            <Link href="/cart">
              <Button variant="ghost" size="icon" className="relative">
                <ShoppingCart className="h-5 w-5" style={{ color: primaryColor }} />
                <span className="absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  {cartCount}
                </span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
      {showColorPicker && (
        <ColorPicker
          onColorSelect={(color) => {
            setPrimaryColor(color);
            setShowColorPicker(false);
          }}
          onClose={() => setShowColorPicker(false)}
        />
      )}
    </header>
  );
}