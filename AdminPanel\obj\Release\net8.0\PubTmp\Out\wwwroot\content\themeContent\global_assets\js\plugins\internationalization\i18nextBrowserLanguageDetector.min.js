!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o():"function"==typeof define&&define.amd?define(o):e.i18nextBrowserLanguageDetector=o()}(this,function(){"use strict";function e(e){return i.call(a.call(arguments,1),function(o){if(o)for(var t in o)void 0===e[t]&&(e[t]=o[t])}),e}function o(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}function t(){return{order:["querystring","cookie","localStorage","navigator","htmlTag"],lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"]}}var n=[],i=n.forEach,a=n.slice,r={create:function(e,o,t,n){var i=void 0;if(t){var a=new Date;a.setTime(a.getTime()+60*t*1e3),i="; expires="+a.toGMTString()}else i="";n=n?"domain="+n+";":"",document.cookie=e+"="+o+i+";"+n+"path=/"},read:function(e){for(var o=e+"=",t=document.cookie.split(";"),n=0;n<t.length;n++){for(var i=t[n];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(o))return i.substring(o.length,i.length)}return null},remove:function(e){this.create(e,"",-1)}},u={name:"cookie",lookup:function(e){var o=void 0;if(e.lookupCookie&&"undefined"!=typeof document){var t=r.read(e.lookupCookie);t&&(o=t)}return o},cacheUserLanguage:function(e,o){o.lookupCookie&&"undefined"!=typeof document&&r.create(o.lookupCookie,e,o.cookieMinutes,o.cookieDomain)}},c={name:"querystring",lookup:function(e){var o=void 0;if("undefined"!=typeof window)for(var t=window.location.search.substring(1),n=t.split("&"),i=0;i<n.length;i++){var a=n[i].indexOf("=");if(a>0){var r=n[i].substring(0,a);r===e.lookupQuerystring&&(o=n[i].substring(a+1))}}return o}},l=void 0;try{l="undefined"!==window&&null!==window.localStorage;var s="i18next.translate.boo";window.localStorage.setItem(s,"foo"),window.localStorage.removeItem(s)}catch(g){l=!1}var f={name:"localStorage",lookup:function(e){var o=void 0;if(e.lookupLocalStorage&&l){var t=window.localStorage.getItem(e.lookupLocalStorage);t&&(o=t)}return o},cacheUserLanguage:function(e,o){o.lookupLocalStorage&&l&&window.localStorage.setItem(o.lookupLocalStorage,e)}},d={name:"navigator",lookup:function(e){var o=[];if("undefined"!=typeof navigator){if(navigator.languages)for(var t=0;t<navigator.languages.length;t++)o.push(navigator.languages[t]);navigator.userLanguage&&o.push(navigator.userLanguage),navigator.language&&o.push(navigator.language)}return o.length>0?o:void 0}},v={name:"htmlTag",lookup:function(e){var o=void 0,t=e.htmlTag||("undefined"!=typeof document?document.documentElement:null);return t&&"function"==typeof t.getAttribute&&(o=t.getAttribute("lang")),o}},h=function(){function e(e,o){for(var t=0;t<o.length;t++){var n=o[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(o,t,n){return t&&e(o.prototype,t),n&&e(o,n),o}}(),p=function(){function n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(this,n),this.type="languageDetector",this.detectors={},this.init(e,t)}return h(n,[{key:"init",value:function(o){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.services=o,this.options=e(n,this.options||{},t()),this.i18nOptions=i,this.addDetector(u),this.addDetector(c),this.addDetector(f),this.addDetector(d),this.addDetector(v)}},{key:"addDetector",value:function(e){this.detectors[e.name]=e}},{key:"detect",value:function(e){var o=this;e||(e=this.options.order);var t=[];e.forEach(function(e){if(o.detectors[e]){var n=o.detectors[e].lookup(o.options);n&&"string"==typeof n&&(n=[n]),n&&(t=t.concat(n))}});var n=void 0;return t.forEach(function(e){if(!n){var t=o.services.languageUtils.formatLanguageCode(e);o.services.languageUtils.isWhitelisted(t)&&(n=t)}}),n||this.i18nOptions.fallbackLng[0]}},{key:"cacheUserLanguage",value:function(e,o){var t=this;o||(o=this.options.caches),o&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||o.forEach(function(o){t.detectors[o]&&t.detectors[o].cacheUserLanguage(e,t.options)}))}}]),n}();return p.type="languageDetector",p});