import { NextRequest, NextResponse } from 'next/server';
import { Config } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Extract JWT token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;

    // Prepare headers for the remote API call
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (!token) {
      return NextResponse.json(
        { error: 'Authorization token is required' },
        { status: 401 }
      );
    }

    // Add JWT token to headers
    headers['Authorization'] = `Bearer ${token}`;
    headers['Token'] = token;

    // Forward the request to the remote API
    const response = await fetch(
      `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/update-address`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      console.error('External API error:', data);
      return NextResponse.json(
        { error: 'Failed to update address', details: data },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in update-address API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}