"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { MakeApiCallAsync, Config } from "@/lib/api-helper";

interface User {
  UserId: number;
  UserName: string;
  Email: string;
  FirstName: string;
  LastName: string;
  PhoneNumber: string;
  ResponseMsg: string;
  [key: string]: any;
}

interface UserContextType {
  user: User | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  login: (
    email: string,
    password: string,
    recaptchaToken?: string
  ) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  token: string | null;
  updateProfile: (profileData: Partial<User>) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  console.log("🚀 UserProvider: Component is rendering");

  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  console.log("🔧 UserProvider: Initial state set, will fetch auth data");

  // Initialize authentication state from server-side data
  React.useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log("🔍 UserProvider: Initializing auth from server");
        const response = await fetch('/api/auth/get-token', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("🔍 UserProvider: Auth API response:", data);

        // Check if we got a token from the API
        if (data.token) {
          console.log("✅ UserProvider: Token found, setting token state");
          setToken(data.token);
        }

        // Try to get user data from cookies
        const cookies = document.cookie.split(';');
        let userData = null;
        
        for (let cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name === 'auth_user') {
            try {
              userData = JSON.parse(decodeURIComponent(value));
              console.log("✅ UserProvider: User data found in cookies");
              break;
            } catch (e) {
              console.error("Error parsing user cookie:", e);
            }
          }
        }

        if (userData && data.token) {
          console.log("✅ UserProvider: User is authenticated, setting state");
          setUser(userData);
        } else {
          console.log("❌ UserProvider: User is not authenticated");
          setUser(null);
          setToken(null);
        }
      } catch (error) {
        console.error("❌ UserProvider: Error fetching auth data:", error);
        setUser(null);
        setToken(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []); // Empty dependency array - run once on mount

  console.log("🔧 UserProvider: Auth data fetch logic defined");

  const login = async (
    email: string,
    password: string,
    recaptchaToken?: string
  ): Promise<{ success: boolean; message: string }> => {
    try {
      setIsLoading(true);

      const headers = {
        Accept: "application/json",
        "Content-Type": "application/json",
      };

      const param = {
        requestParameters: {
          Email: email,
          Password: password,
          ...(recaptchaToken && { RecaptchaToken: recaptchaToken }),
        },
      };

      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.GET_USER_LOGIN,
        null,
        param,
        headers,
        "POST",
        true
      );

      console.log("=== LOGIN API RESPONSE DEBUG ===");
      console.log("Full response:", response);
      console.log("response.data:", response.data);
      console.log("response.data.data:", response.data.data);
      console.log("isAuthorized:", response.data?.isAuthorized);
      console.log("errorMessage:", response.data?.errorMessage);

      if (response && response.data) {
        // Don't check isAuthorized first - check the actual data content
        // The API seems to return isAuthorized: false even for successful logins
        
        // Check for explicit error messages first
        if (response.data.errorMessage) {
          return {
            success: false,
            message: response.data.errorMessage || "Login failed. Please try again.",
          };
        }
        let userData;

        try {
          // Handle the complex nested JSON structure
          if (typeof response.data.data === "string") {
            // First level: Parse the outer JSON string
            const outerData = JSON.parse(response.data.data);
            console.log("Outer data:", outerData);

            if (
              Array.isArray(outerData) &&
              outerData.length > 0 &&
              outerData[0].DATA
            ) {
              // Second level: Parse the inner DATA field
              const innerDataString = outerData[0].DATA;
              console.log("Inner DATA string:", innerDataString);
              
              const innerData = JSON.parse(innerDataString);
              console.log("Parsed inner data:", innerData);
              
              // Check if inner data is empty array (login failed)
              if (Array.isArray(innerData) && innerData.length === 0) {
                console.log("Login failed - empty DATA array");
                return {
                  success: false,
                  message: "Invalid email or password. Please check your credentials and try again.",
                };
              }
              
              userData = innerData;
            } else {
              userData = outerData;
            }
          } else {
            userData = response.data.data;
          }

          console.log("Final userData:", userData);

          // Check if userData is an array and has valid login response
          if (Array.isArray(userData) && userData.length > 0) {
            const userInfo = userData[0];

            // Check if we have actual user data (not empty)
            // Success: userData contains user info with UserID or UserId
            // Failure: userData is empty array [] or contains no user identifier
            console.log("Checking userInfo:", userInfo);
            console.log("Available fields in userInfo:", userInfo ? Object.keys(userInfo) : 'null');
            console.log("UserInfo Gender:", userInfo?.Gender);
            console.log("UserInfo CategoryId:", userInfo?.CategoryId);
            console.log("UserInfo CategoryID:", userInfo?.CategoryID);
            
            if (userInfo && (userInfo.UserID || userInfo.UserId || userInfo.ID || userInfo.Id)) {
              
              // Map the response fields to our expected format
              const userId = userInfo.UserID || userInfo.UserId || userInfo.ID || userInfo.Id;
              console.log("Found user ID:", userId);
              
              const mappedUser = {
                ...userInfo, // Include all original fields FIRST
                // Then override with normalized field names
                UserId: userId,
                UserID: userId, // Keep both for compatibility
                UserName: ((userInfo.FirstName || '') + " " + (userInfo.LastName || '')).trim() || userInfo.UserName || userInfo.Name,
                Email: userInfo.EmailAddress || userInfo.Email || userInfo.email,
                EmailAddress: userInfo.EmailAddress || userInfo.Email || userInfo.email, // Keep both
                FirstName: userInfo.FirstName || userInfo.firstname,
                LastName: userInfo.LastName || userInfo.lastname,
                PhoneNumber: userInfo.PhoneNo || userInfo.MobileNo || userInfo.PhoneNumber,
                PhoneNo: userInfo.PhoneNo || userInfo.MobileNo,
                MobileNo: userInfo.MobileNo || userInfo.PhoneNo,
                ResponseMsg: userInfo.ResponseMsg || "Login successful!",
                Pointno: userInfo.Pointno || userInfo.Points || 0,
              };

              console.log("Mapped user:", mappedUser);

              // Save user data securely using cookies
              try {
                setUser(mappedUser);

                // Save token if provided - it's directly in response.data.token
                const authToken = response.data.token || null;
                
                console.log("Extracted token:", authToken ? `${authToken.substring(0, 20)}...` : 'No token found');
                
                if (authToken) {
                  setToken(authToken);
                } else {
                  console.warn("No JWT token found in login response");
                  setToken(null);
                }

                // Save to secure HttpOnly cookies via API
                try {
                  const cookieResponse = await fetch('/api/auth/set-cookies', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                      user: mappedUser,
                      token: authToken || null
                    })
                  });

                  if (cookieResponse.ok) {
                    console.log('🔐 Secure HttpOnly cookies set successfully');
                  } else {
                    console.warn('Failed to set secure cookies');
                  }
                } catch (cookieError) {
                  console.warn('Cookie API failed:', cookieError);
                }

                console.log("User data saved successfully to secure cookies");
                return { success: true, message: "Login successful!" };
              } catch (saveError) {
                console.error("Error saving user data:", saveError);
                return { success: false, message: "Login successful but failed to save user data" };
              }
            } else {
              // User data exists but no UserID - this shouldn't happen in success case
              return {
                success: false,
                message: userInfo.ResponseMsg || "Invalid credentials. Please try again.",
              };
            }
          } else {
            // Handle the case where userData is empty array [] - this means login failed
            // This is the case: "data": "[{\"DATA\":\"[]\"}]"
            console.log("Empty user data - login failed");
            
            return { 
              success: false, 
              message: "Invalid email or password. Please check your credentials and try again." 
            };
          }
        } catch (parseError) {
          console.error("Error parsing login response:", parseError);
          return {
            success: false,
            message: "Error processing login response!",
          };
        }
      } else {
        return {
          success: false,
          message: "Login failed. Please try again.",
        };
      }
    } catch (error) {
      console.error("Login error:", error);
      return {
        success: false,
        message: "An error occurred. Please try again!",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setUser(null);
    setToken(null);

    // Clear secure HttpOnly cookies via API
    try {
      const response = await fetch('/api/auth/clear-cookies', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        console.log('🔐 Secure cookies cleared successfully');
      } else {
        console.warn('Failed to clear secure cookies via API');
      }
    } catch (error) {
      console.warn('Cookie clearing API failed:', error);
    }

    // Complete cleanup handled by secure cookie API
  };

  const updateProfile = (profileData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...profileData };
      setUser(updatedUser);
      // TODO: Update user info in secure cookies via API if needed
    }
  };

  // Comment out the complex useEffect for now
  /*
  // Initialize user from secure cookies on mount
  useEffect(() => {
    console.log("🚀 UserContext: useEffect triggered - starting initialization");

    const initializeUser = async () => {
      try {
        console.log("🔄 UserContext: Starting user initialization...");

        // Since HttpOnly cookies can't be read directly by client-side JS,
        // we need to get the authentication state from the server
        console.log("🔄 UserContext: Fetching authentication state from server...");

        const response = await fetch('/api/auth/get-token', {
          method: 'GET',
          credentials: 'include', // Include cookies in the request
        });

        console.log("🔄 UserContext: Server response status:", response.status);

        if (response.ok) {
          const data = await response.json();
          console.log("🔄 UserContext: Server authentication state:", data);

          if (data.isAuthenticated && data.user && data.token) {
            console.log("✅ UserContext: Valid user found from server:", data.user);
            console.log("✅ UserContext: Setting user state...");
            setUser(data.user);
            setToken(data.token);
            console.log("✅ UserContext: User state set successfully");
          } else {
            console.log("❌ UserContext: No valid authentication found on server", {
              isAuthenticated: data.isAuthenticated,
              hasUser: !!data.user,
              hasToken: !!data.token
            });
            setUser(null);
            setToken(null);
          }
        } else {
          console.log("❌ UserContext: Failed to get authentication state from server, status:", response.status);
          setUser(null);
          setToken(null);
        }
      } catch (error) {
        console.error("❌ UserContext: Error initializing user from server:", error);
        console.error("❌ UserContext: Error stack:", error.stack);
        setUser(null);
        setToken(null);
      } finally {
        console.log("🏁 UserContext: Setting isLoading to false");
        setIsLoading(false);
      }
    };

    console.log("🔄 UserContext: Calling initializeUser function");
    initializeUser();
  }, []);
  */

  console.log("🔧 UserProvider: useEffect defined successfully");

  const isLoggedIn = user !== null && ((user.UserId && user.UserId > 0) || ((user as any).UserID && (user as any).UserID > 0));
  
  // Debug logging
  console.log("🔍 UserProvider: Authentication state check", {
    user: user,
    userExists: user !== null,
    userId: user?.UserId,
    userID: (user as any)?.UserID,
    isLoggedIn: isLoggedIn,
    token: token ? 'exists' : 'null'
  });

  return (
    <UserContext.Provider
      value={{
        user,
        isLoggedIn,
        isLoading,
        login,
        logout,
        token,
        updateProfile,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}
