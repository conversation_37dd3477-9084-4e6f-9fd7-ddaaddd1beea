import { NextRequest, NextResponse } from "next/server";
import { Config } from "@/lib/config";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Extract JWT token from Authorization header
    const authHeader = request.headers.get("authorization");
    const token = authHeader?.startsWith("Bearer ")
      ? authHeader.substring(7)
      : authHeader;

    console.log("🏠 Debug - Auth header:", authHeader);
    console.log(
      "🏠 Debug - Extracted token:",
      token ? `${token.substring(0, 20)}...` : "missing"
    );

    if (!token) {
      console.error("🏠 No token available for API call");
      return NextResponse.json(
        { error: "Authorization token is required" },
        { status: 401 }
      );
    }

    // Try to decode JWT token to check if it's valid
    try {
      const tokenParts = token.split(".");
      if (tokenParts.length === 3) {
        const payload = JSON.parse(
          Buffer.from(tokenParts[1], "base64").toString()
        );
        console.log("🏠 Token payload:", {
          unique_name: payload.unique_name,
          exp: payload.exp,
          iat: payload.iat,
          iss: payload.iss,
          currentTime: Math.floor(Date.now() / 1000),
        });

        // Check if token is expired
        if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
          console.error("🏠 Token is expired!");
          return NextResponse.json(
            {
              error: "Token is expired",
              code: "TOKEN_EXPIRED",
              message: "Please log in again to continue",
            },
            { status: 401 }
          );
        }
      }
    } catch (e) {
      console.error("🏠 Failed to decode token:", e);
    }

    // Prepare headers for the remote API call
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Accept: "application/json",
      Authorization: `Bearer ${token}`,
    };

    console.log("🏠 Making API call with headers:", Object.keys(headers));
    console.log("🏠 Request body:", JSON.stringify(body, null, 2));
    console.log(
      "🏠 Full request URL:",
      `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-user-address`
    );

    // Forward the request to the remote API
    const response = await fetch(
      `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-user-address`,
      {
        method: "POST",
        headers,
        body: JSON.stringify(body),
      }
    );

    const data = await response.json();

    // If first attempt fails with authorization, try with different header format
    if (data.statusCode === 501 && data.isAuthorized === false) {
      console.log(
        "🏠 First attempt failed, trying with Token header instead..."
      );

      const alternativeHeaders: Record<string, string> = {
        "Content-Type": "application/json",
        Accept: "application/json",
        Token: token, // Try with just Token header
      };

      const retryResponse = await fetch(
        `${Config.ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-user-address`,
        {
          method: "POST",
          headers: alternativeHeaders,
          body: JSON.stringify(body),
        }
      );

      const retryData = await retryResponse.json();
      console.log("🏠 Retry response:", retryData);

      if (retryData.statusCode === 200) {
        return NextResponse.json(retryData);
      }
    }
    console.log("🏠 API Route Response Status:", response.status);
    console.log("🏠 API Route Response Data:", JSON.stringify(data, null, 2));

    // Check if the response indicates authorization issues
    if (
      data.statusCode === 501 ||
      data.statusCode === 401 ||
      data.isAuthorized === false
    ) {
      console.error(
        "🏠 Authorization failed - Token may be invalid or expired"
      );
      console.error("🏠 Response details:", data);
    }

    if (!response.ok) {
      console.error("🏠 External API error:", data);
      return NextResponse.json(
        { error: "Failed to fetch addresses", details: data },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in get-user-addresses API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
