"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useCart } from "@/contexts/cart-context";
import { useUser } from "@/contexts/user-context";
import { useCoupon } from "@/contexts/coupon-context";
import { useCurrency } from "@/contexts/currency-context";
import { useSettings } from "@/contexts/settings-context";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";
import {
  ChevronRight,
  CreditCard,
  DollarSign,
  AlertCircle,
  Star,
  User,
  LogIn,
  Smartphone,
  Building,
  Gift,
  MapPin,
  Home,
} from "lucide-react";
import Swal from "sweetalert2";

export default function CheckoutPage() {
  const { t, primaryColor } = useSettings();
  const { user, isLoggedIn, token } = useUser();
  const { items, total, subtotal, totalIQD, subtotalIQD, clearCart } =
    useCart();
  const { appliedCoupon } = useCoupon();
  const { formatIQD, formatUSD } = useCurrency();
  const router = useRouter();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    number | null
  >(null);
  const [paymentMethods, setPaymentMethods] = useState<any[]>([]);
  const [loadingPaymentMethods, setLoadingPaymentMethods] = useState(true);
  const [showUSD, setShowUSD] = useState(false);
  const [usePoints, setUsePoints] = useState(false);
  const [shippingAddress, setShippingAddress] = useState({
    address: "",
    cityId: "",
    countryId: "107", // Default to CountryID 107
    zipCode: "",
  });
  const [countries, setCountries] = useState<
    Array<{ CountryID: number; CountryName: string; Name?: string }>
  >([]);
  const [cities, setCities] = useState<
    Array<{ CityID: number; CityName: string; Name?: string }>
  >([]);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  const [savedAddresses, setSavedAddresses] = useState<any[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<number | null>(
    null
  );
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  const [showManualAddress, setShowManualAddress] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoggedIn) {
      router.push("/login");
    }
  }, [isLoggedIn, router]);

  // Fetch saved addresses
  useEffect(() => {
    if (isLoggedIn && token) {
      fetchSavedAddresses();
    }
  }, [isLoggedIn, token]);

  // Calculate credit discount (get from localStorage or state)
  const userPoints = user?.Pointno || 0;
  const pointsDiscount = usePoints ? userPoints : 0;
  const pointsDiscountIQD = Math.round(pointsDiscount * 1500);

  // Calculate final totals with all discounts
  const finalTotal = Math.max(
    0,
    total - (appliedCoupon ? appliedCoupon.discount : 0) - pointsDiscount
  );
  const finalTotalIQD = Math.max(
    0,
    totalIQD -
      (appliedCoupon ? Math.round(appliedCoupon.discount * 1500) : 0) -
      pointsDiscountIQD
  );

  // Fetch saved addresses
  const fetchSavedAddresses = async () => {
    setLoadingAddresses(true);
    try {
      const response = await fetch("/api/addresses/get-user-addresses", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          requestParameters: {
            recordValueJson: "[]",
          },
        }),
      });

      const data = await response.json();
      if (data.statusCode === 200 && data.data) {
        const addressesData =
          typeof data.data === "string" ? JSON.parse(data.data) : data.data;
        setSavedAddresses(addressesData || []);
      }
    } catch (error) {
      console.error("Error fetching saved addresses:", error);
    } finally {
      setLoadingAddresses(false);
    }
  };

  // Fetch payment methods from API
  const fetchPaymentMethods = async () => {
    try {
      setLoadingPaymentMethods(true);
      const response = await fetch("/api/payment-methods", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          requestParameters: {},
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.statusCode === 200 && data.data) {
          const methods = JSON.parse(data.data);
          setPaymentMethods(methods);
          // Set default payment method to the first one
          if (methods.length > 0) {
            setSelectedPaymentMethod(methods[0].PaymentMethodID);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching payment methods:", error);
      // Fallback to default payment methods if API fails
      setPaymentMethods([]);
    } finally {
      setLoadingPaymentMethods(false);
    }
  };

  // Get credit usage from localStorage and load countries
  useEffect(() => {
    const savedUsePoints = localStorage.getItem("usePoints") === "true";
    setUsePoints(savedUsePoints);
    fetchCountries();
    fetchPaymentMethods();
    // Load cities for default country (107)
    fetchCities(107);
  }, []);

  const fetchCountries = async () => {
    setLoadingCountries(true);
    try {
      console.log("Fetching countries...");
      const response = await fetch("/api/countries", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          requestParameters: {
            recordValueJson: "[]",
          },
        }),
      });

      console.log("Countries response status:", response.status);
      const data = await response.json();
      console.log("Countries response data:", data);

      if (data && data.data) {
        const parsedData = JSON.parse(data.data);
        console.log("Parsed countries data:", parsedData);
        if (Array.isArray(parsedData)) {
          setCountries(parsedData);
          console.log("Countries set:", parsedData.length, "countries");
        } else {
          console.error("Parsed data is not an array:", parsedData);
        }
      } else {
        console.error("No data in response:", data);
        // Add fallback countries for testing
        const fallbackCountries = [
          { CountryID: 1, CountryName: "United States", Name: "United States" },
          { CountryID: 2, CountryName: "Canada", Name: "Canada" },
          {
            CountryID: 3,
            CountryName: "United Kingdom",
            Name: "United Kingdom",
          },
          { CountryID: 4, CountryName: "Germany", Name: "Germany" },
          { CountryID: 5, CountryName: "France", Name: "France" },
        ];
        setCountries(fallbackCountries);
        console.log("Using fallback countries");
      }
    } catch (error) {
      console.error("Error fetching countries:", error);
      // Add fallback countries on error
      const fallbackCountries = [
        { CountryID: 1, CountryName: "United States", Name: "United States" },
        { CountryID: 2, CountryName: "Canada", Name: "Canada" },
        { CountryID: 3, CountryName: "United Kingdom", Name: "United Kingdom" },
        { CountryID: 4, CountryName: "Germany", Name: "Germany" },
        { CountryID: 5, CountryName: "France", Name: "France" },
      ];
      setCountries(fallbackCountries);
      console.log("Using fallback countries due to error");
    } finally {
      setLoadingCountries(false);
    }
  };

  const fetchCities = async (countryId: number) => {
    setLoadingCities(true);
    setCities([]); // Clear previous cities
    try {
      const response = await fetch("/api/cities", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          requestParameters: {
            StateProvinceId: null,
            CountryId: countryId,
            recordValueJson: "[]",
          },
        }),
      });

      const data = await response.json();
      if (data && data.data) {
        const parsedData = JSON.parse(data.data);
        if (Array.isArray(parsedData)) {
          setCities(parsedData);
        }
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
    } finally {
      setLoadingCities(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setShippingAddress((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCountryChange = (countryId: string) => {
    setShippingAddress((prev) => ({
      ...prev,
      countryId: countryId,
      cityId: "", // Reset city when country changes
    }));

    if (countryId) {
      fetchCities(parseInt(countryId));
    } else {
      setCities([]);
    }
  };

  const handleCityChange = (cityId: string) => {
    setShippingAddress((prev) => ({
      ...prev,
      cityId: cityId,
    }));
  };

  const handleAddressSelection = (addressId: number) => {
    const selectedAddress = savedAddresses.find(
      (addr) => addr.AddressID === addressId
    );
    if (selectedAddress) {
      setSelectedAddressId(addressId);
      setShippingAddress({
        address: `${selectedAddress.AddressLineOne}${
          selectedAddress.AddressLineTwo
            ? ", " + selectedAddress.AddressLineTwo
            : ""
        }`,
        cityId: selectedAddress.CityID?.toString() || "",
        countryId: selectedAddress.CountryID.toString(),
        zipCode: selectedAddress.PostalCode || "",
      });
      setShowManualAddress(false);
    }
  };

  const handleManualAddressToggle = () => {
    setShowManualAddress(true);
    setSelectedAddressId(null);
    setShippingAddress({
      address: "",
      cityId: "",
      countryId: "107",
      zipCode: "",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user is authenticated
    if (!isLoggedIn) {
      Swal.fire({
        title: "Login Required",
        text: "Please login to complete your purchase",
        icon: "info",
        showCancelButton: true,
        confirmButtonText: "Login",
        cancelButtonText: "Cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          router.push("/login?redirect=checkout");
        }
      });
      return;
    }

    // Check if payment method is selected
    if (!selectedPaymentMethod) {
      Swal.fire({
        title: "Error",
        text: "Please select a payment method",
        icon: "error",
      });
      return;
    }

    // Basic validation for shipping address
    if (!selectedAddressId && !shippingAddress.address) {
      Swal.fire({
        title: "Address Required",
        text: "Please select a saved address or enter a new shipping address",
        icon: "error",
      });
      return;
    }

    if (!shippingAddress.address) {
      Swal.fire({
        title: "Error",
        text: "Please provide a shipping address",
        icon: "error",
      });
      return;
    }

    if (!shippingAddress.countryId) {
      Swal.fire({
        title: "Error",
        text: "Please select a country",
        icon: "error",
      });
      return;
    }

    if (!shippingAddress.cityId) {
      Swal.fire({
        title: "Error",
        text: "Please select a city",
        icon: "error",
      });
      return;
    }

    try {
      // Show loading state
      Swal.fire({
        title: "Processing",
        text: "Please wait while we process your order...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      // Prepare cart items in the format expected by the API
      const cartItems = items.map((item) => ({
        ProductId: item.id,
        Quantity: item.quantity,
        Price: parseFloat((item.discountPrice || item.price).toFixed(2)),
        ItemPriceTotal: parseFloat(
          ((item.discountPrice || item.price) * item.quantity).toFixed(2)
        ),
        ItemSubTotal: parseFloat(
          ((item.discountPrice || item.price) * item.quantity).toFixed(2)
        ),
        IsShippingFree: true,
        ShippingChargesTotal: 0.0,
        OrderItemAttributeChargesTotal: 0.0,
        DiscountId: appliedCoupon?.discountTypeId || null,
        CouponCode: appliedCoupon?.code || "",
        DiscountedPrice: item.discountPrice
          ? parseFloat(item.discountPrice.toFixed(2))
          : null,
        OrderItemDiscountTotal: item.discountPrice
          ? parseFloat(
              ((item.price - item.discountPrice) * item.quantity).toFixed(2)
            )
          : 0.0,
        IsDiscountCalculated: false,
        ProductAllSelectedAttributes: JSON.stringify(item.attributes || []),
      }));

      // Get selected payment method details
      const selectedPaymentMethodDetails = paymentMethods.find(
        (pm) => pm.PaymentMethodID === selectedPaymentMethod
      );
      const paymentMethodName =
        selectedPaymentMethodDetails?.PaymentMethodName ||
        "Unknown Payment Method";

      // Check if user is logged in (UserID will be extracted from JWT token)
      if (!user) {
        throw new Error("User not found. Please login again.");
      }

      // Get country and city names for order note
      const selectedCountry = countries.find(
        (c) => c.CountryID.toString() === shippingAddress.countryId
      );
      const selectedCity = cities.find(
        (c) => c.CityID.toString() === shippingAddress.cityId
      );
      const countryName =
        selectedCountry?.CountryName ||
        selectedCountry?.Name ||
        "Unknown Country";
      const cityName =
        selectedCity?.CityName || selectedCity?.Name || "Unknown City";

      // Prepare order data matching the direct API format (no requestParameters wrapper)
      // UserID removed - will be auto-injected from JWT token
      const orderData = {
        OrderNote: `Order from WEB app${
          appliedCoupon ? ` - Coupon: ${appliedCoupon.code}` : ""
        }${
          usePoints ? ` - Credit used: ${pointsDiscount}` : ""
        } - Payment: ${paymentMethodName} - Address: ${
          shippingAddress.address
        }, ${countryName}, ${cityName}`,
        cartJsonData: JSON.stringify(cartItems),
        OrderTotal: parseFloat(finalTotal.toFixed(2)),
        CouponCode: appliedCoupon?.code || "",
        Description: `Order placed via WEB checkout - Payment Method: ${paymentMethodName}`,
        StripeStatus: "",
        StripeResponseJson: "",
        StripeBalanceTransactionId: "",
        StripeChargeId: "",
        PayPalResponseJson: "",
        CurrencyCode: "USD", // Always USD as requested
        PaymentMethod: selectedPaymentMethod,
        Point: usePoints && pointsDiscount > 0 ? pointsDiscount : null,
        ...(selectedAddressId && { AddressID: selectedAddressId }),
      };

      // Log order data for debugging
      console.log("Order data being sent:", orderData);

      // Prepare headers with JWT token
      const orderHeaders: Record<string, string> = {
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      // Add JWT token to headers if available
      if (token) {
        orderHeaders["Authorization"] = `Bearer ${token}`;
        console.log("🔐 Added JWT token to order placement request");
      }

      // Make API call to place order through our API route
      const response = await fetch("/api/orders/post-order", {
        method: "POST",
        headers: orderHeaders,
        body: JSON.stringify(orderData),
      });

      const responseData = await response.json();
      console.log("Order response:", responseData);

      if (response.ok && responseData.data) {
        // Handle the new direct endpoint response format
        const orderInfo = responseData.data;

        // Check if order was placed successfully
        const isSuccess =
          orderInfo &&
          (orderInfo.message === "Order Placed Successfully" ||
            orderInfo.orderID);

        if (isSuccess) {
          // Show beautiful success alert
          Swal.fire({
            title: "🎉 Order Placed Successfully!",
            html: `
              <div style="text-align: center; padding: 20px;">
                <div style="font-size: 48px; margin-bottom: 20px;">✅</div>
                <h3 style="color: #10B981; margin-bottom: 15px;">Thank you for your order!</h3>
                ${
                  orderInfo.orderID
                    ? `<p><strong>Order ID:</strong> #${orderInfo.orderID}</p>`
                    : ""
                }
                ${
                  orderInfo.orderNumber
                    ? `<p><strong>Order Number:</strong> ${orderInfo.orderNumber}</p>`
                    : ""
                }
                <p style="margin-top: 15px; color: #6B7280;">
                  <strong>Total:</strong> $${finalTotal.toFixed(2)}
                </p>
                <p style="color: #6B7280;">
                  We will contact you soon.
                </p>
              </div>
            `,
            icon: "success",
            confirmButtonText: "View My Orders",
            confirmButtonColor: primaryColor,
            showCancelButton: true,
            cancelButtonText: "Continue Shopping",
            allowOutsideClick: false,
            showCloseButton: false,
            customClass: {
              popup: "swal-wide",
              actions: "swal-actions-visible",
            },
            didOpen: () => {
              // Add custom styles for the success alert
              const style = document.createElement("style");
              style.textContent = `
                .swal-wide {
                  width: 600px !important;
                  max-width: 90vw !important;
                }
                .swal2-html-container {
                  font-family: inherit !important;
                }
                .swal-actions-visible .swal2-actions {
                  display: flex !important;
                  justify-content: center !important;
                  gap: 10px !important;
                  margin-top: 20px !important;
                  visibility: visible !important;
                  opacity: 1 !important;
                }
                .swal2-confirm, .swal2-cancel {
                  display: inline-block !important;
                  margin: 0 5px !important;
                  visibility: visible !important;
                  opacity: 1 !important;
                  background-color: #6B7280 !important;
                  color: white !important;
                  border: none !important;
                  padding: 10px 20px !important;
                  border-radius: 6px !important;
                  cursor: pointer !important;
                }
                .swal2-confirm {
                  background-color: ${primaryColor} !important;
                }
                .swal2-cancel:hover, .swal2-confirm:hover {
                  opacity: 0.9 !important;
                  transform: translateY(-1px) !important;
                }
              `;
              document.head.appendChild(style);
            },
          }).then((result) => {
            clearCart();
            // Clear credit usage from localStorage
            localStorage.removeItem("usePoints");

            if (result.isConfirmed) {
              router.push("/orders");
            } else {
              router.push("/");
            }
          });
        } else {
          throw new Error(orderInfo?.message || "Order placement failed");
        }
      } else {
        // Log the full response for debugging
        console.error("Order placement failed. Response:", responseData);
        throw new Error(
          responseData?.error ||
            responseData?.details?.ErrorMessage ||
            "Order placement failed"
        );
      }
    } catch (error) {
      console.error("Error placing order:", error);

      // Show more specific error message
      const errorMessage =
        error instanceof Error
          ? error.message
          : "There was an error processing your order. Please try again.";

      Swal.fire({
        title: "Order Failed",
        text: errorMessage,
        icon: "error",
        confirmButtonText: "Try Again",
        footer: "If the problem persists, please contact support.",
      });
    }
  };

  if (items.length === 0) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Your cart is empty</h1>
          <p className="text-muted-foreground mb-4">
            Add items to your cart to proceed to checkout
          </p>
          <Button asChild>
            <Link href="/">Continue Shopping</Link>
          </Button>
        </div>
      </div>
    );
  }

  // Show login prompt if not authenticated
  if (!isLoggedIn) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card className="max-w-md mx-auto">
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <LogIn className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-lg font-medium mb-2">Login Required</h3>
            <p className="text-muted-foreground mb-4">
              Please log in to continue with checkout
            </p>
            <Button asChild className="w-full">
              <Link href="/login">Login to Continue</Link>
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/cart">Cart</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Checkout</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card>
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* User Information Display */}
              <div className="space-y-4">
                <h2 className="text-xl font-bold flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Account Information
                </h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Name</p>
                      <p className="font-medium">
                        {user?.FirstName} {user?.LastName}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <p className="font-medium">
                        {user?.Email || user?.EmailAddress}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <p className="font-medium">
                        {user?.PhoneNumber || user?.PhoneNo || user?.MobileNo}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">User ID</p>
                      <p className="font-medium">
                        #{user?.UserID || user?.UserId}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-bold">Shipping Address</h2>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => router.push("/addresses")}
                    className="text-sm"
                  >
                    Manage Addresses
                  </Button>
                </div>

                {/* Loading State for Addresses */}
                {loadingAddresses && (
                  <div className="space-y-3">
                    <Label>Loading saved addresses...</Label>
                    <div className="space-y-2">
                      {[1, 2, 3].map((i) => (
                        <div
                          key={i}
                          className="p-3 border rounded-lg animate-pulse"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-gray-200 rounded w-16"></div>
                              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                            </div>
                            <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Saved Addresses Selection */}
                {!loadingAddresses &&
                  savedAddresses.length > 0 &&
                  !showManualAddress && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-base font-semibold">
                          Select Delivery Address
                        </Label>
                        <span className="text-sm text-gray-500">
                          {savedAddresses.length} saved address
                          {savedAddresses.length !== 1 ? "es" : ""}
                        </span>
                      </div>
                      <div className="space-y-3">
                        {savedAddresses.map((address) => (
                          <div
                            key={address.AddressID}
                            className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                              selectedAddressId === address.AddressID
                                ? "border-primary bg-primary/5 shadow-sm"
                                : "border-gray-200 hover:border-gray-300"
                            }`}
                            onClick={() =>
                              handleAddressSelection(address.AddressID)
                            }
                          >
                            {/* Selection Indicator */}
                            {selectedAddressId === address.AddressID && (
                              <div className="absolute top-3 right-3">
                                <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                                  <svg
                                    className="w-4 h-4 text-white"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                </div>
                              </div>
                            )}

                            <div className="flex items-start gap-3">
                              {/* Address Type Icon */}
                              <div className="flex-shrink-0 mt-1">
                                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                  {address.AddressTypeID === 1 ? (
                                    <svg
                                      className="w-4 h-4 text-primary"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                    </svg>
                                  ) : address.AddressTypeID === 2 ? (
                                    <svg
                                      className="w-4 h-4 text-primary"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" />
                                    </svg>
                                  ) : address.AddressTypeID === 3 ? (
                                    <svg
                                      className="w-4 h-4 text-primary"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                  ) : address.AddressTypeID === 4 ? (
                                    <svg
                                      className="w-4 h-4 text-primary"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                    </svg>
                                  ) : (
                                    <svg
                                      className="w-4 h-4 text-primary"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                  )}
                                </div>
                              </div>

                              {/* Address Details */}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                    {(address as any).AddressTypeName ||
                                      (address.AddressTypeID === 1
                                        ? "Home"
                                        : address.AddressTypeID === 2
                                        ? "Billing"
                                        : address.AddressTypeID === 3
                                        ? "Shipping"
                                        : address.AddressTypeID === 4
                                        ? "Mailing"
                                        : "Other")}
                                  </span>
                                  {address.IsDefault && (
                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                      Default
                                    </span>
                                  )}
                                </div>
                                <p className="font-medium text-gray-900 mb-1">
                                  {address.AddressLineOne}
                                </p>
                                {address.AddressLineTwo && (
                                  <p className="text-sm text-gray-600 mb-1">
                                    {address.AddressLineTwo}
                                  </p>
                                )}
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                  <span>{address.CityName || "City"}</span>
                                  <span>•</span>
                                  <span>{address.CountryName}</span>
                                  {address.PostalCode && (
                                    <>
                                      <span>•</span>
                                      <span>{address.PostalCode}</span>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Add New Address Button */}
                      <div className="pt-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleManualAddressToggle}
                          className="w-full border-dashed border-2 hover:border-primary hover:bg-primary/5 transition-colors"
                        >
                          <svg
                            className="w-4 h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 4v16m8-8H4"
                            />
                          </svg>
                          Add New Address
                        </Button>
                      </div>
                    </div>
                  )}

                {/* No Addresses State */}
                {!loadingAddresses &&
                  savedAddresses.length === 0 &&
                  !showManualAddress && (
                    <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
                      <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg
                          className="w-6 h-6 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No saved addresses
                      </h3>
                      <p className="text-gray-500 mb-4">
                        Add your first delivery address to continue
                      </p>
                      <Button
                        type="button"
                        onClick={() => setShowManualAddress(true)}
                        className="inline-flex items-center"
                      >
                        <svg
                          className="w-4 h-4 mr-2"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4v16m8-8H4"
                          />
                        </svg>
                        Add Address
                      </Button>
                    </div>
                  )}

                {/* Manual Address Form */}
                {showManualAddress && (
                  <>
                    {savedAddresses.length > 0 && (
                      <div className="mb-4">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setShowManualAddress(false);
                            setSelectedAddressId(null);
                          }}
                          className="flex items-center gap-2"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 19l-7-7 7-7"
                            />
                          </svg>
                          Back to Saved Addresses
                        </Button>
                      </div>
                    )}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-medium mb-4 flex items-center gap-2">
                        <svg
                          className="w-5 h-5 text-primary"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4v16m8-8H4"
                          />
                        </svg>
                        Add New Address
                      </h3>
                      <div>
                        <Label htmlFor="address">Address *</Label>
                        <Input
                          id="address"
                          name="address"
                          value={shippingAddress.address}
                          onChange={handleInputChange}
                          placeholder="Enter your shipping address"
                          required
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="country">Country *</Label>
                          <Select
                            value={shippingAddress.countryId}
                            onValueChange={handleCountryChange}
                            disabled={loadingCountries}
                          >
                            <SelectTrigger>
                              <SelectValue
                                placeholder={
                                  loadingCountries
                                    ? "Loading countries..."
                                    : "Select country"
                                }
                              />
                            </SelectTrigger>
                            <SelectContent className="max-h-[200px] bg-white">
                              {countries.length > 0 ? (
                                countries.map((country) => (
                                  <SelectItem
                                    key={country.CountryID}
                                    value={country.CountryID.toString()}
                                    className="text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100"
                                  >
                                    {country.CountryName || country.Name}
                                  </SelectItem>
                                ))
                              ) : (
                                <div className="p-2 text-sm text-gray-500">
                                  {loadingCountries
                                    ? "Loading..."
                                    : "No countries found"}
                                </div>
                              )}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="city">City</Label>
                          <Select
                            value={shippingAddress.cityId}
                            onValueChange={handleCityChange}
                            disabled={
                              !shippingAddress.countryId || loadingCities
                            }
                          >
                            <SelectTrigger>
                              <SelectValue
                                placeholder={
                                  !shippingAddress.countryId
                                    ? "Select country first"
                                    : loadingCities
                                    ? "Loading cities..."
                                    : "Select city"
                                }
                              />
                            </SelectTrigger>
                            <SelectContent className="max-h-[200px] bg-white">
                              {!shippingAddress.countryId ? (
                                <div className="p-2 text-sm text-gray-500">
                                  Select country first
                                </div>
                              ) : cities.length > 0 ? (
                                cities.map((city) => (
                                  <SelectItem
                                    key={city.CityID}
                                    value={city.CityID.toString()}
                                    className="text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100"
                                  >
                                    {city.CityName || city.Name}
                                  </SelectItem>
                                ))
                              ) : (
                                <div className="p-2 text-sm text-gray-500">
                                  {loadingCities
                                    ? "Loading cities..."
                                    : "No cities found"}
                                </div>
                              )}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="zipCode">ZIP Code</Label>
                          <Input
                            id="zipCode"
                            name="zipCode"
                            value={shippingAddress.zipCode}
                            onChange={handleInputChange}
                            placeholder="Enter ZIP code"
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>

              <div className="space-y-4">
                <h2 className="text-xl font-bold">Payment Method</h2>

                {loadingPaymentMethods ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {[...Array(6)].map((_, index) => (
                      <div
                        key={index}
                        className="flex flex-col items-center p-4 border rounded-md"
                      >
                        <div className="w-12 h-12 rounded-full bg-gray-200 animate-pulse mb-3"></div>
                        <div className="h-4 w-20 bg-gray-200 rounded animate-pulse mb-2"></div>
                        <div className="w-4 h-4 rounded-full border bg-gray-200 animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {paymentMethods.map((method) => (
                      <div
                        key={method.PaymentMethodID}
                        className="flex flex-col items-center p-4 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors relative"
                        onClick={() =>
                          setSelectedPaymentMethod(method.PaymentMethodID)
                        }
                        style={{
                          borderColor:
                            selectedPaymentMethod === method.PaymentMethodID
                              ? primaryColor
                              : "",
                        }}
                      >
                        {/* Radio button in top-right corner */}
                        <div
                          className="absolute top-3 right-3 w-5 h-5 rounded-full border flex items-center justify-center"
                          style={{ borderColor: primaryColor }}
                        >
                          {selectedPaymentMethod === method.PaymentMethodID && (
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: primaryColor }}
                            ></div>
                          )}
                        </div>

                        {/* Payment method icon */}
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center overflow-hidden mb-3"
                          style={{ backgroundColor: `${primaryColor}20` }}
                        >
                          {method.ImageUrl ? (
                            <img
                              src={method.ImageUrl}
                              alt={method.PaymentMethodName}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                // Fallback to icon if image fails to load
                                e.currentTarget.style.display = "none";
                                const nextElement = e.currentTarget
                                  .nextElementSibling as HTMLElement;
                                if (nextElement) {
                                  nextElement.style.display = "block";
                                }
                              }}
                            />
                          ) : null}
                          <CreditCard
                            className="h-6 w-6"
                            style={{
                              color: primaryColor,
                              display: method.ImageUrl ? "none" : "block",
                            }}
                          />
                        </div>

                        {/* Payment method name */}
                        <div className="text-center">
                          <p className="font-medium text-sm">
                            {method.PaymentMethodName}
                          </p>
                          {method.Description && (
                            <p className="text-xs text-muted-foreground mt-1">
                              {method.Description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Payment Instructions */}
                {selectedPaymentMethod &&
                  (() => {
                    const selectedMethod = paymentMethods.find(
                      (pm) => pm.PaymentMethodID === selectedPaymentMethod
                    );
                    if (selectedMethod && selectedMethod.Instructions) {
                      return (
                        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <h4 className="font-semibold text-blue-800 mb-2">
                            Payment Instructions
                          </h4>
                          <div className="text-sm text-blue-700">
                            <p>{selectedMethod.Instructions}</p>
                            {selectedMethod.PaymentDetails && (
                              <p className="font-medium mt-2">
                                {selectedMethod.PaymentDetails}
                              </p>
                            )}
                            <p className="mt-2 text-xs">
                              After payment, we will contact you to confirm your
                              order.
                            </p>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  })()}

                <div className="mt-4">
                  <Link
                    href="/payment-methods"
                    className="text-sm text-primary hover:underline"
                  >
                    View detailed payment instructions
                  </Link>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full opacity-100 visible"
                style={{
                  backgroundColor: primaryColor,
                  color: "white",
                  opacity: "1",
                  visibility: "visible",
                  display: "block",
                }}
              >
                Place Order
              </Button>
            </form>
          </Card>
        </div>

        <div className="lg:col-span-1">
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold">Order Summary</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowUSD(!showUSD)}
                  className="text-xs"
                >
                  <DollarSign className="h-3 w-3 mr-1" />
                  {showUSD ? "IQD" : "USD"}
                </Button>
              </div>

              <div className="space-y-4 mb-6">
                {items.map((item) => (
                  <div
                    key={item.id}
                    className="flex justify-between items-start"
                  >
                    <div className="flex items-start gap-2">
                      <div className="w-16 h-16 bg-muted rounded-md overflow-hidden">
                        <img
                          src={item.image || `/products/book${item.id}.jpg`}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-sm">{item.name}</p>
                        <p className="text-xs text-muted-foreground">
                          Qty: {item.quantity}
                        </p>

                        {/* Display Selected Attributes */}
                        {item.attributes && item.attributes.length > 0 && (
                          <div className="mt-1">
                            <p className="text-xs font-medium text-gray-600 mb-1">
                              Selected Options:
                            </p>
                            <div className="space-y-1">
                              {item.attributes.map((attr, index) => (
                                <div
                                  key={index}
                                  className="text-xs text-gray-500"
                                >
                                  <span className="font-medium">
                                    {attr.DisplayName || attr.AttributeName}:
                                  </span>{" "}
                                  <span>{attr.AttributeValueText}</span>
                                  {attr.PriceAdjustment &&
                                    attr.PriceAdjustment !== 0 && (
                                      <span className="text-green-600 ml-1">
                                        (
                                        {attr.PriceAdjustmentType === 1
                                          ? "+"
                                          : ""}
                                        {attr.PriceAdjustmentType === 1
                                          ? formatUSD(attr.PriceAdjustment)
                                          : `+${attr.PriceAdjustment}%`}
                                        )
                                      </span>
                                    )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    <p className="font-medium text-sm">
                      {showUSD
                        ? formatUSD(
                            item.adjustedPrice ||
                              item.discountPrice ||
                              item.price
                          )
                        : formatIQD(
                            item.adjustedIqdPrice ||
                              item.iqdPrice ||
                              Math.round(
                                (item.discountPrice || item.price) * 1500
                              )
                          )}
                    </p>
                  </div>
                ))}
              </div>

              {/* Selected Address Display */}
              {(selectedAddressId || shippingAddress.address) && (
                <div className="border-t pt-4 mb-4">
                  <h3 className="font-medium mb-2 flex items-center gap-2">
                    <svg
                      className="w-4 h-4 text-primary"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    Delivery Address
                  </h3>
                  <div className="bg-gray-50 rounded-lg p-3">
                    {selectedAddressId ? (
                      (() => {
                        const selectedAddress = savedAddresses.find(
                          (addr) => addr.AddressID === selectedAddressId
                        );
                        return selectedAddress ? (
                          <div className="text-sm">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                {(selectedAddress as any).AddressTypeName ||
                                  (selectedAddress.AddressTypeID === 1
                                    ? "Home"
                                    : selectedAddress.AddressTypeID === 2
                                    ? "Billing"
                                    : selectedAddress.AddressTypeID === 3
                                    ? "Shipping"
                                    : selectedAddress.AddressTypeID === 4
                                    ? "Mailing"
                                    : "Other")}
                              </span>
                            </div>
                            <p className="font-medium text-gray-900">
                              {selectedAddress.AddressLineOne}
                            </p>
                            {selectedAddress.AddressLineTwo && (
                              <p className="text-gray-600">
                                {selectedAddress.AddressLineTwo}
                              </p>
                            )}
                            <p className="text-gray-600">
                              {selectedAddress.CityName || "City"} •{" "}
                              {selectedAddress.CountryName}
                              {selectedAddress.PostalCode &&
                                ` • ${selectedAddress.PostalCode}`}
                            </p>
                          </div>
                        ) : null;
                      })()
                    ) : (
                      <div className="text-sm">
                        <p className="font-medium text-gray-900">
                          {shippingAddress.address}
                        </p>
                        <p className="text-gray-600">
                          {cities.find(
                            (c) =>
                              c.CityID.toString() === shippingAddress.cityId
                          )?.CityName || "City"}{" "}
                          •{" "}
                          {countries.find(
                            (c) =>
                              c.CountryID.toString() ===
                              shippingAddress.countryId
                          )?.CountryName || "Country"}
                          {shippingAddress.zipCode &&
                            ` • ${shippingAddress.zipCode}`}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="space-y-3 border-t pt-4">
                {/* Subtotal */}
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Subtotal</span>
                  <span className="font-medium">
                    {showUSD ? formatUSD(subtotal) : formatIQD(subtotalIQD)}
                  </span>
                </div>

                {/* Coupon Discount */}
                {appliedCoupon && (
                  <div className="flex justify-between text-green-600">
                    <span>Coupon ({appliedCoupon.code})</span>
                    <span>
                      -
                      {showUSD
                        ? formatUSD(appliedCoupon.discount)
                        : formatIQD(Math.round(appliedCoupon.discount * 1500))}
                    </span>
                  </div>
                )}

                {/* Credit Discount */}
                {usePoints && pointsDiscount > 0 && (
                  <div className="flex justify-between text-yellow-600">
                    <span className="flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      Credit Discount
                    </span>
                    <span>-${pointsDiscount.toFixed(2)}</span>
                  </div>
                )}

                {/* Total */}
                <div className="border-t pt-3 mt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold">Total</span>
                    <div className="text-right">
                      <div
                        className="text-xl font-bold"
                        style={{ color: primaryColor }}
                      >
                        {showUSD
                          ? formatUSD(finalTotal)
                          : formatIQD(finalTotalIQD)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        ≈{" "}
                        {showUSD
                          ? formatIQD(finalTotalIQD)
                          : formatUSD(finalTotal)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Savings Summary */}
                {(appliedCoupon || (usePoints && pointsDiscount > 0)) && (
                  <div className="bg-green-50 rounded-lg p-3 border border-green-200">
                    <div className="text-sm text-green-700">
                      <div className="font-medium mb-1">💰 Total Savings:</div>
                      <div className="font-bold">
                        {showUSD
                          ? formatUSD(
                              (appliedCoupon ? appliedCoupon.discount : 0) +
                                pointsDiscount
                            )
                          : formatIQD(
                              (appliedCoupon
                                ? Math.round(appliedCoupon.discount * 1500)
                                : 0) + pointsDiscountIQD
                            )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
