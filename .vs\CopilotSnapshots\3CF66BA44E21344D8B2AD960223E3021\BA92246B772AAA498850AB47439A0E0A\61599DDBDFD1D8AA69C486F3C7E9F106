﻿using AdminPanel.Helpers.EmailSenderHelper;
using DAL.Repository.IServices;
using DAL.DBContext;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Spreadsheet;
using Entities.DBInheritedModels;
using Entities.DBModels;
using Helpers.ApiHelpers;
using Helpers.AuthorizationHelpers;
using Helpers.AuthorizationHelpers.JwtTokenHelper;
using Helpers.CommonHelpers;
using Helpers.CommonHelpers.Enums;
using Helpers.CommonHelpers.ICommonHelpers;
using Helpers.ConversionHelpers;
using Helpers.ConversionHelpers.IConversionHelpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using Stripe;
using Stripe.FinancialConnections;
using System.Diagnostics;
using System.Security.Policy;
using Dapper;

namespace AdminPanel.Areas.V1.Controllers
{
    [Route("api/v1/common")] //-- "common" is controller name with out api keyword"
    [ApiController]
    [Area("V1")]
    public class ApiCommonController : ControllerBase
    {

        private readonly IApiOperationServicesDAL _apiOperationServicesDAL;
        private readonly ICalculationHelper _calculationHelper;
        private readonly ICommonServicesDAL _commonServicesDAL;
        private readonly ISessionManager _sessionManag;
        private readonly IConstants _constants;
        private readonly IUserManagementServicesDAL _userManagementServicesDAL;
        private readonly IConfiguration _configuration;
        private readonly IEmailSender _emailSender;
        private readonly IOrderHelper _orderHelper;
        private readonly IProductServicesDAL _productServicesDAL;
        private readonly IBasicDataServicesDAL _basicDataServicesDAL;
        private readonly ISalesServicesDAL _salesServicesDAL;
        private readonly IFilesHelpers _filesHelpers;
        private readonly IDataContextHelper _contextHelper;

        public ApiCommonController(IApiOperationServicesDAL apiOperationServices, ICommonServicesDAL commonServicesDAL, ISessionManager sessionManag,
            IConstants constants, ICalculationHelper calculationHelper, IUserManagementServicesDAL userManagementServicesDAL, IConfiguration configuration,
            IEmailSender emailSender, IOrderHelper orderHelper, IProductServicesDAL productServicesDAL, IBasicDataServicesDAL basicDataServicesDAL, ISalesServicesDAL salesServicesDAL,
            IFilesHelpers filesHelpers, IDataContextHelper contextHelper)
        {
            this._apiOperationServicesDAL = apiOperationServices;
            this._commonServicesDAL = commonServicesDAL;
            this._sessionManag = sessionManag;
            this._constants = constants;
            this._calculationHelper = calculationHelper;
            this._userManagementServicesDAL = userManagementServicesDAL;
            this._configuration = configuration;
            this._emailSender = emailSender;
            this._orderHelper = orderHelper;
            this._productServicesDAL = productServicesDAL;
            this._basicDataServicesDAL = basicDataServicesDAL;
            this._salesServicesDAL = salesServicesDAL;
            this._filesHelpers = filesHelpers;
            this._contextHelper = contextHelper;
        }


        [Route("validate-email-send-otp/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> ValidateEmailAndSendOTP(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {


                if (param != null && param.Count != 0)
                {
                    Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }


                    string? Email = requestParameters != null ? requestParameters["Email"].ToString() : "";
                    if (String.IsNullOrEmpty(Email))
                    {

                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please fill email field!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }


                    //-- 1. Valiedate email from data base if exists
                    var user = await _userManagementServicesDAL.GetUserByEmailAddressDAL(Email);
                    if (user == null || user.UserId < 1)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Incorrect email. Please enter your correct email address!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    //-- 2. Generate OTP and save in database
                    int OTP = CommonConversionHelper.GenerateRandomNumber();
                    string OTPResponseFromDB = await this._userManagementServicesDAL.SaveOTPLogInformationDAL((short)ApiStatusCodes.OK, "Peding", "OTP Generated", OTP, null, Email, null, null, true, null);

                    if (String.IsNullOrEmpty(OTPResponseFromDB) || OTPResponseFromDB != "Saved Successfully!")
                    {
                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "An error occured in saving OTP. Please try again!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    //-- 3. Send OTP in email to user
                    try
                    {
                        List<EmailAddressEntity> emailAddresses = new List<EmailAddressEntity>();
                        emailAddresses.Add(new EmailAddressEntity { DisplayName = "User", Address = Email });
                        string SiteTitle = _configuration.GetSection("AppSetting").GetSection("WebsiteTitle").Value;
                        var message = new EmailMessage(emailAddresses, "Recover Password", String.Format("Your OTP for password recovery is: {0}", OTP), String.Format("{0} , Recover Password", SiteTitle));
                        _emailSender.SendEmail(message);
                    }
                    catch (Exception ex)
                    {
                        await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);

                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "An error occured in sending email. Please try again!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }


                    //-- 4. return user success and lets user enter otp that he recieve in email and password and confirm password
                    #region result

                    result.Data = "[]";
                    result.StatusCode = 200;
                    result.StatusMessage = "Ok";
                    result.Message = "Sent Successfully";
                    result.ErrorMessage = String.Empty;
                    apiActionResult = new APIActionResult(result);

                    #endregion


                }
                else
                {
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "An error is occured while processing your request.";
                    apiActionResult = new APIActionResult(result);
                }

            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }


            return apiActionResult;
        }


        [Route("validate-otp-change-password/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> ValidateOTPAndChangePassword(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {


                if (param != null && param.Count != 0)
                {
                    Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }


                    string? Email = requestParameters != null ? requestParameters["Email"].ToString() : "";
                    int? Otp = requestParameters != null ? Convert.ToInt32(requestParameters["Otp"].ToString()) : 0;
                    string? Password = requestParameters != null ? requestParameters["Password"].ToString() : "";
                    string? ConfirmPassword = requestParameters != null ? requestParameters["ConfirmPassword"].ToString() : "";



                    #region validation area

                    if (String.IsNullOrEmpty(Email))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please fill email field!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (Otp == null)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please fill OTP field!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (String.IsNullOrEmpty(Password))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please enter password!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (String.IsNullOrEmpty(ConfirmPassword))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please enter confirm password!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (Password.Length < 6 || ConfirmPassword.Length < 6)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Password & Confirm Password fields lenght should not be less than 6 characters!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (Password != ConfirmPassword)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Password does not match!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    #endregion


                    //-- 1. Valiedate email from data base if exists
                    var user = await _userManagementServicesDAL.GetUserByEmailAddressDAL(Email);
                    if (user == null || user.UserId < 1)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Incorrect email. Please try again!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    //-- 2. Validate the OTP from data base
                    var IsValidOTP = await this._userManagementServicesDAL.ValidateOTPByEmailDAL(Email, Convert.ToInt32(Otp));

                    //--Update the OTP Count by Email
                    string UpdateOTPResponse = await this._userManagementServicesDAL.UpdateOTPAttemptsByEmailDAL(Email);


                    if (IsValidOTP != null && !String.IsNullOrWhiteSpace(IsValidOTP.EmailAddress))
                    {

                        string PasswordResetResponse = "";
                        //-- 3. Reset user password
                        Password = CommonConversionHelper.Encrypt(Password);
                        PasswordResetResponse = await this._userManagementServicesDAL.ResetUserPasswordDAL(Email, Password);


                        //--De activate otps by email address
                        string DeActivateResponse = await this._userManagementServicesDAL.DeActivateOTPsByEmail(Email);



                        if (PasswordResetResponse == "Saved Successfully!")
                        {
                            #region result

                            result.Data = "[]";
                            result.StatusCode = 200;
                            result.StatusMessage = "Ok";
                            result.Message = "Password reset successfully";
                            result.ErrorMessage = String.Empty;
                            apiActionResult = new APIActionResult(result);

                            #endregion

                        }
                        else
                        {
                            result.StatusCode = 204;
                            result.StatusMessage = "Error";
                            result.ErrorMessage = "An error occured. Please try again";
                            apiActionResult = new APIActionResult(result);
                            return apiActionResult;
                        }


                    }
                    else
                    {

                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Invalid OTP that you enter!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }



                }
                else
                {
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "An error is occured while processing your request.";
                    apiActionResult = new APIActionResult(result);
                }

            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }


            return apiActionResult;
        }


        [Route("test-notifications")]
        [HttpGet]
        public async Task<APIActionResult> TestNotifications()
        {
            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            result.ActionType = ActionTypeEnum.JSON;

            try
            {
                using (var repo = _contextHelper.GetDataContextHelper())
                {
                    var notifications = repo.Fetch<dynamic>("SELECT TOP 5 * FROM AdminPanelNotifications ORDER BY CreatedOn DESC");
                    result.Data = notifications;
                    result.StatusCode = 200;
                    result.StatusMessage = "Ok";
                    result.ErrorMessage = string.Empty;
                }

                await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                result.Data = null;
                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = ex.Message;
            }

            var apiActionResult = new APIActionResult(result);
            return apiActionResult;
        }

        [Route("post-order-direct")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> PostCustomerOrderDirect([FromBody] PostCustomerOrderRequest request)
        {
            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;
            result.ActionType = ActionTypeEnum.JSON;
            try
            {
                // Get user ID from JWT token (automatically extracted by middleware)
                int? authenticatedUserId = this.GetCurrentUserIdAsInt();
                // Use authenticated user ID if available, otherwise fall back to request parameter
                int effectiveUserId = authenticatedUserId ?? request.UserID;
                // Validate user ID
                if (effectiveUserId <= 0)
                    throw new ArgumentException("User authentication required - invalid or missing user ID");
                // If both token and request specify user ID, ensure they match for security
                if (authenticatedUserId.HasValue && request.UserID > 0 && authenticatedUserId.Value != request.UserID)
                    throw new UnauthorizedAccessException("Token user ID does not match request user ID");
                // Update request with authenticated user ID
                request.UserID = effectiveUserId;
                Console.WriteLine($"Order placement - Authenticated User ID: {authenticatedUserId}, Effective User ID: {effectiveUserId}");

                if (string.IsNullOrEmpty(request.cartJsonData))
                    throw new ArgumentException("Cart data is required");
                if (request.OrderTotal <= 0)
                    throw new ArgumentException("Order total must be greater than 0");

                // Parse cart data
                var cartItems = JsonConvert.DeserializeObject<List<CustomerFinalOrderItemData>>(request.cartJsonData);
                if (cartItems == null || !cartItems.Any())
                    throw new ArgumentException("Cart cannot be empty");

                // Get user's shipping address ID
                var userAddressId = _contextHelper.GetDataContextHelper().ExecuteScalar<int?>(
                    "SELECT TOP 1 AddressId FROM UserAddresses WHERE UserId = @0", request.UserID);

                using (var repo = _contextHelper.GetDataContextHelper())
                {
                    int orderId = 0;
                    string orderNumber = "";

                    // Begin transaction for data consistency
                    repo.Execute("BEGIN TRANSACTION");

                    try
                    {
                        Console.WriteLine("Starting order creation process");

                        // Get exchange rate from AppConfigs
                        var exchangeRate = repo.ExecuteScalar<decimal?>("SELECT TRY_CAST(AppConfigValue AS DECIMAL(18,6)) FROM AppConfigs WHERE AppConfigKey = 'currencyprice'") ?? 1;
                        Console.WriteLine($"Exchange rate: {exchangeRate}");

                        // Get user's current point balance (handle missing Pointno column gracefully)
                        decimal currentUserPoints = 0;
                        bool pointsColumnExists = true;
                        try
                        {
                            currentUserPoints = repo.ExecuteScalar<decimal?>("SELECT Pointno FROM Users WHERE UserId = @0", request.UserID) ?? 0;
                            Console.WriteLine($"User current points: {currentUserPoints}");
                        }
                        catch (Exception ex) when (ex.Message.Contains("Invalid column name 'Pointno'"))
                        {
                            Console.WriteLine("Pointno column not found in Users table, defaulting to 0 points for user " + request.UserID);
                            currentUserPoints = 0;
                            pointsColumnExists = false;
                        }

                        // Calculate points to deduct (if points are being used)
                        var pointsToUse = request.Point ?? 0;
                        if (pointsToUse > 0)
                        {
                            if (!pointsColumnExists)
                            {
                                Console.WriteLine("Cannot use points - Pointno column not found. Proceeding with order without point deduction.");
                            }
                            else if (currentUserPoints < pointsToUse)
                            {
                                throw new Exception($"Insufficient points. User has {currentUserPoints} points but trying to use {pointsToUse} points.");
                            }
                            else
                            {
                                // Deduct points from user's balance
                                try
                                {
                                    var newPointBalance = currentUserPoints - pointsToUse;
                                    repo.Execute("UPDATE Users SET Pointno = @0 WHERE UserId = @1", newPointBalance, request.UserID);
                                    Console.WriteLine($"Points deducted. New balance: {newPointBalance}");
                                }
                                catch (Exception ex) when (ex.Message.Contains("Invalid column name 'Pointno'"))
                                {
                                    Console.WriteLine("Cannot update points - Pointno column not found for user " + request.UserID);
                                }
                            }
                        }

                        // Get "Active" order status ID
                        var activeStatusId = repo.ExecuteScalar<int?>("SELECT TOP 1 StatusId FROM OrderStatuses WHERE StatusName = 'Active'") ?? 1;
                        Console.WriteLine($"Active status ID: {activeStatusId}");

                        // Insert order and get ID using direct SQL with additional fields
                        orderId = repo.ExecuteScalar<int>(@"INSERT INTO Orders (CustomerId, OrderDateUtc, ShippingAddressId, OrderTotal, Point, LatestStatusId, ExchangeRate,
                                                     OrderTotalDiscountAmount, OrderTotalShippingCharges, OrderTotalAttributeCharges, OrderTax)
                                                     OUTPUT INSERTED.OrderId
                                                     VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10)",
                            request.UserID, DateTime.UtcNow, userAddressId ?? 0, 0, request.Point ?? (object)DBNull.Value, activeStatusId, exchangeRate,
                            0, 0, 0, 0);

                        // Generate order number and update the order
                        orderNumber = $"OR#{orderId:00000000}";
                        repo.Execute("UPDATE Orders SET OrderNumber = @0 WHERE OrderId = @1", orderNumber, orderId);
                        Console.WriteLine($"Order number updated: {orderNumber}");

                        // Add order status mapping entry
                        repo.Execute(@"INSERT INTO OrderStatusesMapping (OrderId, StatusId, IsActive, CreatedOn, CreatedBy)
                               VALUES (@0, @1, @2, @3, @4)",
                            orderId, activeStatusId, true, DateTime.Now, request.UserID);

                        // Add order note if provided
                        if (!string.IsNullOrEmpty(request.OrderNote))
                        {
                            repo.Execute(@"INSERT INTO OrderNotes (OrderId, Message, CreatedBy, CreatedOn)
                                  VALUES (@0, @1, @2, @3)",
                                orderId, request.OrderNote, request.UserID, DateTime.Now);
                        }

                        Console.WriteLine($"Order ID: {orderId}");
                        Console.WriteLine("Processing order items");
                        decimal calculatedTotal = 0;

                        // Process each cart item
                        foreach (var cartItem in cartItems)
                        {
                            // Calculate Order Item Total = (Price * Quantity) - Discount - Point (handle missing Pointno gracefully)
                            decimal cartItemPoints = 0;
                            try
                            {
                                cartItemPoints = cartItem.Pointno ?? 0;
                            }
                            catch
                            {
                                cartItemPoints = 0; // Default to 0 if Pointno property doesn't exist
                            }

                            // Use ItemSubTotal if non-zero, otherwise calculate
                            decimal itemTotal = cartItem.ItemSubTotal != 0 ? cartItem.ItemSubTotal :
                                ((cartItem.Price * cartItem.Quantity) - (cartItem.OrderItemDiscountTotal ?? 0) - cartItemPoints);

                            Console.WriteLine($"Item {cartItem.ProductId}: Price={cartItem.Price}, Qty={cartItem.Quantity}, Discount={cartItem.OrderItemDiscountTotal ?? 0}, Points={cartItemPoints}, Total={itemTotal}");

                            // Generate OrderItemGuid for this order item
                            var orderItemGuid = Guid.NewGuid();
                            int orderItemId = 0;

                            // Get vendor commission ID
                            int? vendorId = repo.ExecuteScalar<int?>("SELECT TOP 1 VendorID FROM Products WHERE ProductID = @0", cartItem.ProductId);
                            int? vendorCommissionId = null;

                            if (vendorId.HasValue)
                            {
                                vendorCommissionId = repo.ExecuteScalar<int?>(
                                    @"SELECT TOP 1 VendorCommissionID 
                              FROM VendorsCommissionSetup 
                              WHERE UserID = @0 AND IsActive = 1 
                              AND (CAST(GETDATE() AS DATE) BETWEEN CAST(ApplicableFrom AS DATE) AND CAST(ApplicableTo AS DATE))",
                                    vendorId.Value);
                                Console.WriteLine($"Vendor commission found for ProductID {cartItem.ProductId}: VendorCommissionID={vendorCommissionId}");
                            }

                            // Insert order item using direct SQL with additional fields
                            try
                            {
                                orderItemId = repo.ExecuteScalar<int>(@"INSERT INTO OrderItems (OrderId, ProductId, Quantity, Price, ItemPriceTotal,
                                     OrderItemDiscountTotal, OrderItemShippingChargesTotal, OrderItemAttributeChargesTotal,
                                     DiscountId, OrderItemTotal, Pointno, OrderItemGuid, OrderItemTaxTotal, VendorCommissionID)
                                     OUTPUT INSERTED.OrderItemID
                                     VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10, @11, @12, @13)",
                                    orderId, cartItem.ProductId, cartItem.Quantity, cartItem.Price, cartItem.ItemPriceTotal,
                                    cartItem.OrderItemDiscountTotal ?? 0, cartItem.ShippingChargesTotal, cartItem.OrderItemAttributeChargesTotal,
                                    cartItem.DiscountId ?? 0, itemTotal, cartItemPoints, orderItemGuid, 0, vendorCommissionId ?? (object)DBNull.Value);
                            }
                            catch (Exception ex) when (ex.Message.Contains("Invalid column name"))
                            {
                                // Handle missing columns gracefully - fallback to basic insertion
                                Console.WriteLine($"Some columns not found in OrderItems table: {ex.Message}");
                                orderItemId = repo.ExecuteScalar<int>(@"INSERT INTO OrderItems (OrderId, ProductId, Quantity, Price, ItemPriceTotal,
                                         OrderItemDiscountTotal, OrderItemShippingChargesTotal, OrderItemAttributeChargesTotal,
                                         DiscountId, OrderItemTotal, OrderItemGuid, OrderItemTaxTotal)
                                         OUTPUT INSERTED.OrderItemID
                                         VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10, @11)",
                                    orderId, cartItem.ProductId, cartItem.Quantity, cartItem.Price, cartItem.ItemPriceTotal,
                                    cartItem.OrderItemDiscountTotal ?? 0, cartItem.ShippingChargesTotal, cartItem.OrderItemAttributeChargesTotal,
                                    cartItem.DiscountId ?? 0, itemTotal, orderItemGuid, 0);
                            }

                            calculatedTotal += itemTotal;

                            // Update product stock quantity
                            int newStockQuantity = repo.ExecuteScalar<int>("SELECT ISNULL(StockQuantity, 0) - @0 FROM Products WHERE ProductID = @1",
                                cartItem.Quantity, cartItem.ProductId);
                            repo.Execute("UPDATE Products SET StockQuantity = @0 WHERE ProductID = @1",
                                newStockQuantity, cartItem.ProductId);
                            Console.WriteLine($"Product stock updated for ProductID {cartItem.ProductId}: New quantity = {newStockQuantity}");

                            // Insert into discount usage history if discount was used
                            if (cartItem.DiscountId.HasValue && cartItem.DiscountId > 0)
                            {
                                repo.Execute(@"INSERT INTO DiscountUsageHistory(DiscountID, UsedBy, UsageDate)
                                       VALUES (@0, @1, @2)",
                                    cartItem.DiscountId.Value, request.UserID, DateTime.Now);
                                Console.WriteLine($"Discount usage recorded for DiscountID {cartItem.DiscountId.Value}");
                            }

                            // Insert OrderProductAttributeMapping records for product attributes/options
                            try
                            {
                                if (!string.IsNullOrEmpty(cartItem.ProductAllSelectedAttributes))
                                {
                                    var selectedAttributes = JsonConvert.DeserializeObject<List<dynamic>>(cartItem.ProductAllSelectedAttributes);
                                    if (selectedAttributes != null)
                                    {
                                        foreach (var attribute in selectedAttributes)
                                        {
                                            try
                                            {
                                                var productAttributeId = Convert.ToInt32(attribute.ProductAttributeID ?? 0);
                                                var attributeValue = Convert.ToInt32(attribute.PrimaryKeyValue ?? 0);
                                                var additionalPrice = Convert.ToDecimal(attribute.AttrAdditionalPrice ?? 0);

                                                if (productAttributeId > 0 && attributeValue > 0)
                                                {
                                                    repo.Execute(@"INSERT INTO OrderProductAttributeMapping (ProductAttributeID, OrderItemID, AttributeValue, AttrAdditionalPrice)
                                                          VALUES (@0, @1, @2, @3)",
                                                        productAttributeId, orderItemId, attributeValue, additionalPrice);
                                                    Console.WriteLine($"OrderProductAttributeMapping created: ProductAttributeID={productAttributeId}, AttributeValue={attributeValue}");
                                                }
                                            }
                                            catch (Exception attrEx)
                                            {
                                                Console.WriteLine($"Error processing attribute: {attrEx.Message}");
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error creating OrderProductAttributeMapping: {ex.Message}");
                            }
                        }

                        // Calculate and update detailed order totals
                        var orderTotalsQuery = repo.Query<dynamic>(@"SELECT 
                    SUM(ISNULL(OrderItemShippingChargesTotal,0)) AS ShippingCharges,
                    SUM(ISNULL(OrderItemTaxTotal,0)) AS Tax,
                    SUM(ISNULL(OrderItemDiscountTotal,0)) AS DiscountAmount,
                    SUM(ISNULL(OrderItemAttributeChargesTotal,0)) AS AttributeCharges,
                    SUM(ISNULL(OrderItemTotal,0)) AS Total
                    FROM OrderItems WHERE OrderID = @0", orderId).FirstOrDefault();

                        decimal shippingCharges = 0, tax = 0, discountAmount = 0, attributeCharges = 0, total = 0;

                        if (orderTotalsQuery != null)
                        {
                            var totalsDict = orderTotalsQuery as IDictionary<string, object>;
                            if (totalsDict != null)
                            {
                                shippingCharges = Convert.ToDecimal(totalsDict["ShippingCharges"] ?? 0);
                                tax = Convert.ToDecimal(totalsDict["Tax"] ?? 0);
                                discountAmount = Convert.ToDecimal(totalsDict["DiscountAmount"] ?? 0);
                                attributeCharges = Convert.ToDecimal(totalsDict["AttributeCharges"] ?? 0);
                                total = Convert.ToDecimal(totalsDict["Total"] ?? 0);
                            }
                        }

                        repo.Execute(@"UPDATE Orders SET 
                    OrderNumber = @0,
                    OrderTotalShippingCharges = @1,
                    OrderTax = @2,
                    OrderTotalDiscountAmount = @3,
                    OrderTotalAttributeCharges = @4,
                    OrderTotal = @5
                    WHERE OrderID = @6",
                            orderNumber, shippingCharges, tax, discountAmount, attributeCharges, total, orderId);

                        Console.WriteLine($"Order totals updated: Shipping={shippingCharges}, Tax={tax}, " +
                                         $"Discount={discountAmount}, Attributes={attributeCharges}, Total={total}");

                        // Insert OrderShippingDetail records for all order items (matching stored procedure behavior)
                        var orderItems = repo.Query<int>("SELECT OrderItemID FROM OrderItems WHERE OrderID = @0", orderId);
                        foreach (var orderItemId in orderItems)
                        {
                            try
                            {
                                repo.Execute(@"INSERT INTO OrderShippingDetail (OrderID, OrderItemID, ShippingStatusID)
                                      VALUES (@0, @1, @2)",
                                    orderId, orderItemId, activeStatusId);
                                Console.WriteLine($"OrderShippingDetail created for OrderItem {orderItemId}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error creating OrderShippingDetail: {ex.Message}");
                            }
                        }

                        // Digital Products handling
                        int cashOnDeliveryMethod = repo.ExecuteScalar<int?>(
                            "SELECT TOP 1 PaymentMethodID FROM PaymentMethods WHERE PaymentMethodName = 'Cash on delivery'") ?? 6;

                        if (request.PaymentMethod != cashOnDeliveryMethod)
                        {
                            // Update shipping status for digital products to "Completed"
                            int completedStatusId = repo.ExecuteScalar<int?>(
                                "SELECT TOP 1 StatusID FROM OrderStatuses WHERE StatusName = 'Completed'") ?? 2;

                            repo.Execute(@"UPDATE OrderShippingDetail 
                                   SET ShippingStatusID = @0
                                   WHERE OrderItemId IN (
                                       SELECT oi.OrderItemId 
                                       FROM OrderItems oi 
                                       INNER JOIN Products p ON oi.ProductId = p.ProductId 
                                       WHERE oi.OrderID = @1 AND p.IsDigitalProduct = 1
                                   )", completedStatusId, orderId);
                            Console.WriteLine($"Digital product shipping status updated to Completed");

                            // Check if all products are digital
                            int totalDigitalProducts = repo.ExecuteScalar<int>(
                                @"SELECT COUNT(*) 
                          FROM OrderItems oi
                          INNER JOIN Products p ON oi.ProductId = p.ProductId
                          WHERE oi.OrderID = @0 AND p.IsDigitalProduct = 1", orderId);

                            int totalOrderProducts = repo.ExecuteScalar<int>(
                                @"SELECT COUNT(*) 
                          FROM OrderItems oi
                          INNER JOIN Products p ON oi.ProductId = p.ProductId
                          WHERE oi.OrderID = @0", orderId);

                            if (totalDigitalProducts > 0 && totalDigitalProducts == totalOrderProducts)
                            {
                                // All products are digital, mark order as completed
                                repo.Execute(@"INSERT INTO OrderStatusesMapping(OrderID, StatusID, IsActive, CreatedOn, CreatedBy)
                                       VALUES (@0, @1, 1, @2, @3)",
                                    orderId, completedStatusId, DateTime.Now, request.UserID);

                                repo.Execute("UPDATE Orders SET LatestStatusID = @0 WHERE OrderID = @1",
                                    completedStatusId, orderId);
                                Console.WriteLine($"All products are digital, order status updated to Completed");
                            }
                        }

                        // Get CurrencyID from CurrencyCode
                        int currencyId = 1; // Default to 1 if not found
                        if (!string.IsNullOrEmpty(request.CurrencyCode))
                        {
                            currencyId = repo.ExecuteScalar<int?>(
                                "SELECT TOP 1 CurrencyID FROM Currencies WHERE CurrencyCode = @0", request.CurrencyCode) ?? 1;
                            Console.WriteLine($"Currency found: Code={request.CurrencyCode}, ID={currencyId}");
                        }

                        // Create payment record based on payment method (updated to match stored procedure logic)
                        string milestoneName = "Milestone 1";
                        bool isCompleted = true;

                        // Get payment method IDs
                        int? stripePaymentMethod = repo.ExecuteScalar<int?>(
                            "SELECT TOP 1 PaymentMethodID FROM PaymentMethods WHERE PaymentMethodName = 'Stripe'");
                        int? payPalPaymentMethod = repo.ExecuteScalar<int?>(
                            "SELECT TOP 1 PaymentMethodID FROM PaymentMethods WHERE PaymentMethodName = 'PayPal'");

                        // Get final order total for payment
                        var finalOrderTotal = repo.ExecuteScalar<decimal>("SELECT TOP 1 OrderTotal FROM Orders WHERE OrderID = @0", orderId);

                        if (request.PaymentMethod == stripePaymentMethod)
                        {
                            repo.Execute(@"INSERT INTO OrdersPayments (OrderId, PaymentMethodId, MilestoneValue, MilestoneName,
                                 CurrencyId, IsCompleted, PaymentDate, StripeResponseJson, StripeChargeId,
                                 StripeBalanceTransactionId)
                                 VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9)",
                                orderId, request.PaymentMethod, finalOrderTotal, milestoneName, currencyId, isCompleted, DateTime.Now,
                                request.StripeResponseJson ?? (object)DBNull.Value, request.StripeChargeId ?? (object)DBNull.Value,
                                request.StripeBalanceTransactionId ?? (object)DBNull.Value);
                        }
                        else if (request.PaymentMethod == payPalPaymentMethod)
                        {
                            repo.Execute(@"INSERT INTO OrdersPayments (OrderId, PaymentMethodId, MilestoneValue, MilestoneName,
                                 CurrencyId, IsCompleted, PaymentDate, PayPalResponseJson)
                                 VALUES (@0, @1, @2, @3, @4, @5, @6, @7)",
                                orderId, request.PaymentMethod, finalOrderTotal, milestoneName, currencyId, isCompleted, DateTime.Now,
                                request.PayPalResponseJson ?? (object)DBNull.Value);
                        }
                        else
                        {
                            repo.Execute(@"INSERT INTO OrdersPayments (OrderId, PaymentMethodId, MilestoneValue, MilestoneName,
                                 CurrencyId, IsCompleted, PaymentDate)
                                 VALUES (@0, @1, @2, @3, @4, @5, @6)",
                                orderId, request.PaymentMethod, finalOrderTotal, milestoneName, currencyId, isCompleted, DateTime.Now);
                        }
                        Console.WriteLine($"Payment record created for order {orderId} with CurrencyID={currencyId}");

                        // Insert admin panel notification for new order (using stored procedure call if available)
                        Console.WriteLine("Creating notification");
                        try
                        {
                            var firstName = repo.ExecuteScalar<string>("SELECT TOP 1 FirstName FROM Users WHERE UserID = @0", request.UserID) ?? "Customer";
                            var notificationMessage = $"New order {orderNumber} has been placed by {firstName} at {DateTime.Now.Date:yyyy-MM-dd}";

                            // Try to use the stored procedure first (matching the original SP behavior)
                            try
                            {
                                repo.Execute("EXEC [dbo].[SP_InsertAdminPanelNotification] @0, @1, @2, @3",
                                    "New Order Placed", notificationMessage, 1, "");
                                Console.WriteLine("Notification created using stored procedure");
                            }
                            catch
                            {
                                // Fallback to direct insert if stored procedure doesn't exist
                                var notificationId = repo.ExecuteScalar<int>(@"INSERT INTO AdminPanelNotifications (Title, Message, NotificationTypeId, IsRead, ClickUrl, CreatedOn)
                                                      OUTPUT INSERTED.NotificationId
                                                      VALUES (@0, @1, @2, @3, @4, @5)",
                                    "New Order Placed", notificationMessage, 1, false, "", DateTime.Now);
                                Console.WriteLine($"Notification created with ID: {notificationId}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error creating notification: {ex.Message}");
                        }

                        // Commit transaction
                        repo.Execute("COMMIT TRANSACTION");
                        Console.WriteLine("Order creation completed successfully");

                        result.Data = new
                        {
                            OrderID = orderId,
                            OrderNumber = orderNumber,
                            Message = "Order Placed Successfully"
                        };
                        result.StatusCode = 200;
                        result.StatusMessage = "Ok";
                        result.ErrorMessage = string.Empty;
                    }
                    catch (Exception ex)
                    {
                        // Rollback transaction on error
                        try
                        {
                            repo.Execute("ROLLBACK TRANSACTION");
                        }
                        catch { }

                        Console.WriteLine($"Error in order creation: {ex.Message}");
                        throw;
                    }
                }
                await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                result.Data = null;
                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = ex.Message;
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
            }
            apiActionResult = new APIActionResult(result);
            return apiActionResult;
        }

        [Route("post-order/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> PostCustomerOrder(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";
            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;
            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);
            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion
            try
            {
                StripeConfiguration.ApiKey = _constants.GetAppSettingKeyValue("AppSetting", "StripeSecretKey");
                // strip implementation url
                // https://stripe.com/docs/payments/accept-a-payment-charges?html-or-react=react
                var paymentToken = "";
                int PaymentMethod = 0;
                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);
                            paymentToken = requestParameters["paymentToken"].ToString(); // Using ASP.NET MVC 
                            PaymentMethod = Convert.ToInt32(requestParameters["PaymentMethod"].ToString());
                        }
                    }
                }
                //--strip testing card urls: https://stripe.com/docs/testing?numbers-or-method-or-token=card-numbers#visa
                #region new
                string CouponCode = requestParameters["CouponCode"].ToString() ?? "";
                string? Description = "Order of customer id: " + requestParameters["UserID"].ToString() + " at " + DateTime.Now.ToString();
                string cartJsonData = "[]";
                decimal? OrderTotal = 0m;
                decimal? ItemSubTotal = 0;
                cartJsonData = requestParameters != null ? requestParameters["cartJsonData"].ToString() ?? "[]" : "[]";
                var cartCustomerProducts = new List<CartCustomerProducts>();
                cartCustomerProducts = JsonConvert.DeserializeObject<List<CartCustomerProducts>>(cartJsonData);
                if (cartCustomerProducts != null)
                {
                    List<ProductsIds>? ProductIds = new List<ProductsIds>();
                    foreach (var item in cartCustomerProducts)
                    {
                        var rowData = new ProductsIds();
                        rowData.ProductId = item.ProductId;
                        ProductIds.Add(rowData);
                    }
                    string ProductIdsJson = JsonConvert.SerializeObject(ProductIds);
                    //-- get products list by ids
                    Dictionary<string, object>? requestParametersAllProducts = new Dictionary<string, object>();
                    requestParametersAllProducts.Add("ProductsIds", ProductIdsJson);
                    var ApiConfigurationForGetAllProducts = await this._apiOperationServicesDAL.GetAPIConfiguration("get-products-list-by-ids");
                    string? allProductsDataJson = "[]";
                    if (ApiConfigurationForGetAllProducts != null)
                    {
                        allProductsDataJson = await this._apiOperationServicesDAL.GetApiData(requestParametersAllProducts, ApiConfigurationForGetAllProducts);
                    }
                    //--Calcualte Discount for products
                    string productsAfterDiscount = await _calculationHelper.CalculateDiscountsForProducts((allProductsDataJson ?? "[]"));
                    var CartItems = JsonConvert.DeserializeObject<List<ApiProductEntity?>>(productsAfterDiscount ?? "[]");
                    List<CustomerFinalOrderItemData> customerFinalOrderItemDataList = new List<CustomerFinalOrderItemData>();
                    if (CartItems != null)
                    {
                        if (CartItems.Any(x => x.DiscountedPrice > 0))
                        {
                            foreach (var item in CartItems)
                            {
                                //--get product attributes by product id
                                var ApiConfigForProductAttributes = await this._apiOperationServicesDAL.GetAPIConfiguration("get-product-all-attributes-by-productId");
                                var requestParametersAllAttributes = new Dictionary<string, object>();
                                requestParametersAllAttributes.Add("ProductID", item?.ProductId ?? 0);
                                string? productAllAttributesJson = await this._apiOperationServicesDAL.GetApiData(requestParametersAllAttributes, ApiConfigForProductAttributes);
                                var _cartProductAllAttributes = JsonConvert.DeserializeObject<List<CartProductAllAttributes?>>(productAllAttributesJson ?? "[]");
                                var _productSelectedAttributes = cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item?.ProductId)?.productSelectedAttributes;
                                item.Price = item.Price;
                                item.Quantity = Convert.ToInt32(cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item.ProductId)?.Quantity);
                                item.ShippingCharges = item.ShippingCharges * item.Quantity;
                                decimal additionalAttributesCharges = 0;
                                if (_productSelectedAttributes != null)
                                {
                                    for (int index = 0; index < _productSelectedAttributes.Count(); index++)
                                    {
                                        var priceData = _cartProductAllAttributes?.Where(x => x.ProductAttributeID == _productSelectedAttributes[index].ProductAttributeID
                                                        && x.PrimaryKeyValue == _productSelectedAttributes[index].PrimaryKeyValue)?.FirstOrDefault();
                                        additionalAttributesCharges = Convert.ToDecimal(additionalAttributesCharges + priceData?.AdditionalPrice);
                                    }
                                }
                                additionalAttributesCharges = additionalAttributesCharges * item.Quantity;
                                if (item.DiscountId > 0 && item.DiscountedPrice != null && item.DiscountedPrice > 0)
                                {
                                    item.DiscountedPrice = item.DiscountedPrice;
                                    item.OrderItemDiscount = item.Price - item.DiscountedPrice;
                                    item.OrderItemDiscount = item.OrderItemDiscount * item.Quantity;
                                }
                                item.ItemSubTotal = Convert.ToDecimal((item.DiscountedPrice > 0 ? item.DiscountedPrice : item.Price) * (item.Quantity));
                                item.ItemSubTotal = item.ItemSubTotal + (item.ShippingCharges ?? 0);
                                item.ItemSubTotal = item.ItemSubTotal + (additionalAttributesCharges);
                                OrderTotal = Convert.ToDecimal(OrderTotal + (item.ItemSubTotal));
                                //--Set All Selected attributes data for this row
                                item.ProductAllSelectedAttributes = new List<CartProductAllAttributes>();
                                if (_productSelectedAttributes != null)
                                {
                                    foreach (var attr in _productSelectedAttributes)
                                    {
                                        var fullDataAttribue = _cartProductAllAttributes.Where(x => x.ProductAttributeID == attr.ProductAttributeID && x.PrimaryKeyValue == attr.PrimaryKeyValue).FirstOrDefault();
                                        item.ProductAllSelectedAttributes.Add(fullDataAttribue);
                                    }
                                }
                                //--Fill final order item
                                CustomerFinalOrderItemData customerFinalOrderItemData = new CustomerFinalOrderItemData()
                                {
                                    ProductId = item.ProductId,
                                    Quantity = item.Quantity,
                                    Price = item.Price,
                                    ItemPriceTotal = item.Price * item.Quantity,
                                    ItemSubTotal = item.ItemSubTotal ?? 0,
                                    IsShippingFree = item.IsShippingFree ?? true,
                                    ShippingChargesTotal = item.ShippingCharges ?? 0,
                                    OrderItemAttributeChargesTotal = additionalAttributesCharges,
                                    DiscountId = item.DiscountId ?? 0,
                                    DiscountedPrice = item.DiscountedPrice ?? 0,
                                    OrderItemDiscountTotal = item.OrderItemDiscount ?? 0,
                                    IsDiscountCalculated = item.IsDiscountCalculated ?? false,
                                    CouponCode = item.CouponCode ?? "",
                                    ProductAllSelectedAttributes = item.ProductAllSelectedAttributes != null ?
                                        JsonConvert.SerializeObject(item.ProductAllSelectedAttributes) : "[]"
                                };
                                customerFinalOrderItemDataList.Add(customerFinalOrderItemData);
                            }
                        }
                        else if (!String.IsNullOrWhiteSpace(CouponCode))
                        {
                            bool IsDiscountExecuted = false;
                            foreach (var item in CartItems)
                            {
                                //--get product attributes by product id
                                var ApiConfigForProductAttributes = await this._apiOperationServicesDAL.GetAPIConfiguration("get-product-all-attributes-by-productId");
                                var requestParametersAllAttributes = new Dictionary<string, object>();
                                requestParametersAllAttributes.Add("ProductID", item?.ProductId ?? 0);
                                string? productAllAttributesJson = await this._apiOperationServicesDAL.GetApiData(requestParametersAllAttributes, ApiConfigForProductAttributes);
                                var _cartProductAllAttributes = JsonConvert.DeserializeObject<List<CartProductAllAttributes?>>(productAllAttributesJson ?? "[]");
                                var _productSelectedAttributes = cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item?.ProductId)?.productSelectedAttributes;
                                item.Price = item.Price;
                                item.Quantity = Convert.ToInt32(cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item.ProductId)?.Quantity);
                                item.ShippingCharges = item.ShippingCharges * item.Quantity;
                                decimal additionalAttributesCharges = 0;
                                if (_productSelectedAttributes != null)
                                {
                                    for (int index = 0; index < _productSelectedAttributes.Count(); index++)
                                    {
                                        var priceData = _cartProductAllAttributes?.Where(x => x.ProductAttributeID == _productSelectedAttributes[index].ProductAttributeID
                                                        && x.PrimaryKeyValue == _productSelectedAttributes[index].PrimaryKeyValue)?.FirstOrDefault();
                                        additionalAttributesCharges = Convert.ToDecimal(additionalAttributesCharges + priceData?.AdditionalPrice);
                                    }
                                }
                                additionalAttributesCharges = additionalAttributesCharges * item.Quantity;
                                //--Set All Selected attributes data for this row
                                item.ProductAllSelectedAttributes = new List<CartProductAllAttributes>();
                                if (_productSelectedAttributes != null)
                                {
                                    foreach (var attr in _productSelectedAttributes)
                                    {
                                        var fullDataAttribue = _cartProductAllAttributes.Where(x => x.ProductAttributeID == attr.ProductAttributeID && x.PrimaryKeyValue == attr.PrimaryKeyValue).FirstOrDefault();
                                        item.ProductAllSelectedAttributes.Add(fullDataAttribue);
                                    }
                                }
                                //--Fill final order item
                                CustomerFinalOrderItemData customerFinalOrderItemData = new CustomerFinalOrderItemData()
                                {
                                    ProductId = item.ProductId,
                                    Quantity = item.Quantity,
                                    Price = item.Price,
                                    ItemPriceTotal = item.Price * item.Quantity,
                                    IsShippingFree = item.IsShippingFree ?? true,
                                    ShippingChargesTotal = item.ShippingCharges ?? 0,
                                    OrderItemAttributeChargesTotal = additionalAttributesCharges,
                                    CouponCode = item.CouponCode ?? "",
                                    ProductAllSelectedAttributes = item.ProductAllSelectedAttributes != null ?
                                        JsonConvert.SerializeObject(item.ProductAllSelectedAttributes) : "[]"
                                };
                                //--If discount is applied from the coupon to a product then do not execute again for each product
                                if (item.IsDiscountAllowed == true && IsDiscountExecuted == false)
                                {
                                    var couponDiscount = await _calculationHelper.CalculateCouponDiscountValueForProduct(item.ProductId, item.Price, CouponCode, cartJsonData);
                                    if (couponDiscount != null)
                                    {
                                        decimal DiscountValueAfterCouponApplied = Convert.ToDecimal(couponDiscount["DiscountValueAfterCouponApplied"].ToString());
                                        customerFinalOrderItemData.DiscountId = Convert.ToInt32(couponDiscount["DiscountId"].ToString());
                                        customerFinalOrderItemData.CouponCode = CouponCode;
                                        item.OrderItemDiscount = DiscountValueAfterCouponApplied;
                                        item.DiscountedPrice = (item.Price - (DiscountValueAfterCouponApplied < item.Price ? DiscountValueAfterCouponApplied : 0));
                                        customerFinalOrderItemData.DiscountedPrice = item.DiscountedPrice ?? 0;
                                        customerFinalOrderItemData.IsDiscountCalculated = true;
                                        if (Convert.ToInt32(couponDiscount["DiscountTypeId"].ToString()) == (short)DiscountTypesEnum.AppliedOnOrderTotal)
                                        {
                                            customerFinalOrderItemData.OrderItemDiscountTotal = ((item.OrderItemDiscount ?? 0));
                                        }
                                        else
                                        {
                                            customerFinalOrderItemData.OrderItemDiscountTotal = ((item.OrderItemDiscount ?? 0) * item.Quantity);
                                        }
                                        //--set the flag to true
                                        IsDiscountExecuted = DiscountValueAfterCouponApplied > 0 ? true : false;
                                    }
                                }
                                item.ItemSubTotal = Convert.ToDecimal((item.DiscountedPrice > 0 ? item.DiscountedPrice : item.Price) * (item.Quantity));
                                item.ItemSubTotal = item.ItemSubTotal + (item.ShippingCharges ?? 0);
                                item.ItemSubTotal = item.ItemSubTotal + (additionalAttributesCharges);
                                customerFinalOrderItemData.ItemSubTotal = Convert.ToDecimal(item.ItemSubTotal);
                                OrderTotal = Convert.ToDecimal(OrderTotal + (item.ItemSubTotal));
                                customerFinalOrderItemDataList.Add(customerFinalOrderItemData);
                            }
                        }
                        if (OrderTotal == null || OrderTotal == 0 || OrderTotal < 0)
                        {
                            throw new InvalidOperationException("Invalid order total amount!");
                        }
                        //--Get Api Configuration
                        var ApiConfiguration = await this._apiOperationServicesDAL.GetAPIConfiguration(UrlName);

                        // FIXED: Changed .Add() to indexer syntax to prevent duplicate key exception
                        requestParameters["CurrencyCode"] = CommonConversionHelper.GetDefaultCurrencyCode()?.ToLower() ?? "usd";
                        requestParameters["OrderTotal"] = OrderTotal;
                        requestParameters["cartJsonData"] = JsonConvert.SerializeObject(customerFinalOrderItemDataList);

                        if (PaymentMethod == (short)PaymentMethodsEnum.Stripe)
                        {
                            if (String.IsNullOrWhiteSpace(paymentToken))
                            {
                                throw new InvalidOperationException("stripe payment token is empty!");
                            }
                            string currency = CommonConversionHelper.GetDefaultCurrencyCode()?.ToLower() ?? "usd";
                            var options = new ChargeCreateOptions
                            {
                                Amount = currency == "usd" ? (long)(OrderTotal * 100) : (long)OrderTotal,
                                Currency = currency,
                                Description = Description,
                                Source = paymentToken,
                            };
                            var service = new ChargeService();
                            var charge = service.Create(options);
                            if (charge.Status == "succeeded")
                            {
                                // FIXED: Changed .Add() to indexer syntax to prevent duplicate key exception
                                requestParameters["Description"] = Description;
                                requestParameters["StripeStatus"] = charge.Status;
                                requestParameters["StripeResponseJson"] = charge.StripeResponse.Content;
                                requestParameters["StripeBalanceTransactionId"] = charge.BalanceTransactionId;
                                requestParameters["StripeChargeId"] = charge.Id;
                                requestParameters["PayPalResponseJson"] = string.Empty;
                                // Add Point parameter if provided
                                if (requestParameters.ContainsKey("Point") && requestParameters["Point"] != null)
                                {
                                    // Point parameter is already in requestParameters, no need to add it again
                                }
                                else
                                {
                                    requestParameters["Point"] = null;
                                }
                                //--save the information in data base
                                data = await _orderHelper.SaveCustomerOrderInDbWithRetry(requestParameters, ApiConfiguration);
                                #region result
                                result.Data = data;
                                result.StatusCode = 200;
                                result.StatusMessage = "Ok";
                                result.ErrorMessage = String.Empty;
                                apiActionResult = new APIActionResult(result);
                                #endregion
                            }
                            else
                            {
                                #region result
                                result.Data = "[]";
                                result.StatusCode = 501;
                                result.StatusMessage = "Error";
                                result.ErrorMessage = "An error occured. Please try again";
                                apiActionResult = new APIActionResult(result);
                                #endregion
                            }
                        }
                        else if (PaymentMethod == (short)PaymentMethodsEnum.CashOnDelivery)
                        {
                            // FIXED: Changed .Add() to indexer syntax to prevent duplicate key exception
                            requestParameters["Description"] = Description;
                            requestParameters["StripeStatus"] = "";
                            requestParameters["StripeResponseJson"] = "";
                            requestParameters["StripeBalanceTransactionId"] = "";
                            requestParameters["StripeChargeId"] = "";
                            requestParameters["PayPalResponseJson"] = string.Empty;
                            // Add Point parameter if provided
                            if (requestParameters.ContainsKey("Point") && requestParameters["Point"] != null)
                            {
                                // Point parameter is already in requestParameters, no need to add it again
                            }
                            else
                            {
                                requestParameters["Point"] = null;
                            }
                            //--save the information in data base
                            data = await _orderHelper.SaveCustomerOrderInDbWithRetry(requestParameters, ApiConfiguration);
                            #region result
                            result.Data = data;
                            result.StatusCode = 200;
                            result.StatusMessage = "Ok";
                            result.ErrorMessage = String.Empty;
                            apiActionResult = new APIActionResult(result);
                            #endregion
                        }
                        else if (PaymentMethod == (short)PaymentMethodsEnum.PayPal)
                        {
                            // FIXED: Changed .Add() to indexer syntax to prevent duplicate key exception
                            requestParameters["Description"] = Description;
                            requestParameters["StripeStatus"] = "";
                            requestParameters["StripeResponseJson"] = "";
                            requestParameters["StripeBalanceTransactionId"] = "";
                            requestParameters["StripeChargeId"] = "";
                            requestParameters["PayPalResponseJson"] = requestParameters["payPalOrderConfirmJson"].ToString() ?? "";
                            // Add Point parameter if provided
                            if (requestParameters.ContainsKey("Point") && requestParameters["Point"] != null)
                            {
                                // Point parameter is already in requestParameters, no need to add it again
                            }
                            else
                            {
                                requestParameters["Point"] = null;
                            }
                            //--save the information in data base
                            data = await _orderHelper.SaveCustomerOrderInDbWithRetry(requestParameters, ApiConfiguration);
                            #region result
                            result.Data = data;
                            result.StatusCode = 200;
                            result.StatusMessage = "Ok";
                            result.ErrorMessage = String.Empty;
                            apiActionResult = new APIActionResult(result);
                            #endregion
                        }
                        else
                        {
                            #region result
                            result.Data = "[]";
                            result.StatusCode = 501;
                            result.StatusMessage = "Error";
                            result.ErrorMessage = "No payment method specified";
                            apiActionResult = new APIActionResult(result);
                            #endregion
                        }
                        #region Send email to customer if order placed successfully
                        try
                        {
                            if (!string.IsNullOrEmpty(Request.Headers["UserID"]) && result.StatusCode == 200)
                            {
                                int UserID = Convert.ToInt32(Request.Headers["UserID"].ToString());
                                var userData = _basicDataServicesDAL.GetUserDataByUserID(UserID);
                                List<EmailAddressEntity> emailAddresses = new List<EmailAddressEntity>();
                                string content = String.Format("Your order has been placed successfully. Order total amount is: {0} {1}. {2}Please check your order history page for further details. {2}{2} Thanks", (CommonConversionHelper.GetDefaultCurrencyCode()?.ToLower() ?? "USD"), OrderTotal, Environment.NewLine);
                                emailAddresses.Add(new EmailAddressEntity { DisplayName = "User", Address = userData?.EmailAddress });
                                string SiteTitle = _configuration.GetSection("AppSetting").GetSection("WebsiteTitle").Value;
                                var message = new EmailMessage(emailAddresses, "New Order Placed", content, String.Format("{0} , New Order Placed", SiteTitle));
                                _emailSender.SendEmail(message);
                            }
                        }
                        catch (Exception ex)
                        {
                            //-- Do nothing
                            var noThing = ex.Message;
                        }
                        #endregion
                    }
                    else
                    {
                        #region result
                        result.Data = "[]";
                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "No cart itme selected!";
                        apiActionResult = new APIActionResult(result);
                        #endregion
                    }
                }
                else
                {
                    #region result
                    result.Data = "[]";
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "No cart itme selected!";
                    apiActionResult = new APIActionResult(result);
                    #endregion
                }
                #endregion
            }
            catch (Exception ex)
            {
                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion
                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }
            return apiActionResult;
        }

        [Route("get-strp-pub-key/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetStripePublishableKey(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                //-- 4. return user success and lets user enter otp that he recieve in email and password and confirm password
                #region result
                string StripePublishableKey = _constants.GetAppSettingKeyValue("AppSetting", "StripePublishableKey");
                Dictionary<string, string> StripeDic = new Dictionary<string, string>();
                StripeDic.Add("strpK", "StripePublishableKey");

                result.Data = JsonConvert.SerializeObject(StripeDic);
                result.StatusCode = 200;
                result.StatusMessage = "Ok";
                result.Message = "Sent Successfully";
                result.ErrorMessage = String.Empty;
                apiActionResult = new APIActionResult(result);

                #endregion

            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }


            return apiActionResult;
        }

        [Route("get-customer-cart-items/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetCustomerLatestCartItems(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {

                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);



                        }

                    }

                }


                #region calcualtion
                //-- get customer cart data
                string cartJsonData = "[]";
                cartJsonData = requestParameters != null ? requestParameters["cartJsonData"].ToString() ?? "[]" : "[]";
                var apiResponse = await _calculationHelper.CalcualteProductsTotalAndAdditionalPrices(cartJsonData);
                #endregion






                #region result
                result.Data = JsonConvert.SerializeObject(apiResponse);
                result.StatusCode = 200;
                result.StatusMessage = "Ok";
                result.ErrorMessage = String.Empty;
                apiActionResult = new APIActionResult(result);
                #endregion


            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }





            return apiActionResult;


        }

        [Route("get-coupon-code-discount-value/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetCouponCodeDiscountedValue(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {

                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }

                }


                #region new
                string CouponCode = requestParameters["CouponCode"].ToString() ?? "";
                string cartJsonData = "[]";
                cartJsonData = requestParameters != null ? requestParameters["cartJsonData"].ToString() ?? "[]" : "[]";


                var cartCustomerProducts = new List<CartCustomerProducts>();
                cartCustomerProducts = JsonConvert.DeserializeObject<List<CartCustomerProducts>>(cartJsonData);
                if (cartCustomerProducts != null)
                {
                    List<ProductsIds>? ProductIds = new List<ProductsIds>();

                    foreach (var item in cartCustomerProducts)
                    {
                        var rowData = new ProductsIds();
                        rowData.ProductId = item.ProductId;
                        ProductIds.Add(rowData);
                    }

                    string ProductIdsJson = JsonConvert.SerializeObject(ProductIds);

                    //-- get products list by ids
                    Dictionary<string, object>? requestParametersAllProducts = new Dictionary<string, object>();
                    requestParametersAllProducts.Add("ProductsIds", ProductIdsJson);
                    var ApiConfigurationForGetAllProducts = await this._apiOperationServicesDAL.GetAPIConfiguration("get-products-list-by-ids");
                    string? allProductsDataJson = "[]";
                    if (ApiConfigurationForGetAllProducts != null)
                    {
                        allProductsDataJson = await this._apiOperationServicesDAL.GetApiData(requestParametersAllProducts, ApiConfigurationForGetAllProducts);

                    }

                    //--Calcualte Discount for products
                    string productsAfterDiscount = await _calculationHelper.CalculateDiscountsForProducts((allProductsDataJson ?? "[]"));


                    var CartItems = JsonConvert.DeserializeObject<List<ApiProductEntity?>>(productsAfterDiscount ?? "[]");

                    if (CartItems != null)
                    {
                        bool IsDiscountExecuted = false;
                        Dictionary<string, object> apiResponse = new Dictionary<string, object>();
                        foreach (var item in CartItems)
                        {
                            //--If discount is applied from the coupon to a product then do not execute again for each product
                            if (item.IsDiscountAllowed == true && IsDiscountExecuted == false)
                            {
                                var couponDiscount = await _calculationHelper.CalculateCouponDiscountValueForProduct(item.ProductId, item.Price, CouponCode, cartJsonData);
                                if (couponDiscount != null)
                                {
                                    item.Quantity = Convert.ToInt32(cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item.ProductId)?.Quantity);
                                    decimal DiscountValueAfterCouponApplied = Convert.ToDecimal(couponDiscount["DiscountValueAfterCouponApplied"].ToString());
                                    apiResponse.Add("DiscountValueAfterCouponApplied", DiscountValueAfterCouponApplied);
                                    apiResponse.Add("DiscountId", Convert.ToInt32(couponDiscount["DiscountId"].ToString()));
                                   

                                    if (Convert.ToInt32(couponDiscount["DiscountTypeId"].ToString()) == (short)DiscountTypesEnum.AppliedOnOrderTotal)
                                    {
                                        apiResponse.Add("DiscountValueAfterCouponAppliedWithQuantity", (DiscountValueAfterCouponApplied));
                                    }
                                    else
                                    {
                                        apiResponse.Add("DiscountValueAfterCouponAppliedWithQuantity", (DiscountValueAfterCouponApplied * item.Quantity));
                                    }
                                    //--set the flag to true
                                    IsDiscountExecuted = DiscountValueAfterCouponApplied > 0 ? true : false;
                                }
                            }



                        }

                        #region result
                        result.Data = JsonConvert.SerializeObject(apiResponse);
                        result.StatusCode = 200;
                        result.StatusMessage = "Ok";
                        result.ErrorMessage = String.Empty;
                        apiActionResult = new APIActionResult(result);
                        #endregion

                    }
                    else
                    {
                        #region result
                        result.Data = "[]";
                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "No cart itme selected!";
                        apiActionResult = new APIActionResult(result);
                        #endregion

                    }
                }
                else
                {
                    #region result
                    result.Data = "[]";
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "No cart itme selected!";
                    apiActionResult = new APIActionResult(result);
                    #endregion
                }

                #endregion



            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }





            return apiActionResult;


        }

        [Route("localization-cstm-portal/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetLocalizationControlsJsonDataCstmPortal(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {

                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }

                }


                #region new
                int EntityId = 0;
                string langCode = string.Empty;
                if (requestParameters!=null)
                {
                     EntityId = Convert.ToInt32(requestParameters["entityId"].ToString() ?? "0");
                    langCode = requestParameters["languageCode"].ToString() ?? "en";
                }

                ScrnsLocalizationEntity scrnsLocalization = new ScrnsLocalizationEntity()
                {
                    ScreenId = EntityId,
                    AppModuleId = (short)AppModulesEnum.CustomerPortal,
                    LanguageId = CommonConversionHelper.GetLanguageIdbyLanguageCode(langCode)
                };
                var resultLocalization = await _commonServicesDAL.GetScreenLocalizationJsonDataDAL(scrnsLocalization);


                if (resultLocalization != null && !String.IsNullOrWhiteSpace(resultLocalization.LabelsJsonData))
                {
                    #region result
                    result.Data = resultLocalization.LabelsJsonData;
                    result.StatusCode = 200;
                    result.StatusMessage = "Ok";
                    result.ErrorMessage = String.Empty;
                    apiActionResult = new APIActionResult(result);
                    #endregion
                }
                else
                {
                  
                    #region result
                    result.Data = "[]";
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "No cart itme selected!";
                    apiActionResult = new APIActionResult(result);
                    #endregion

                }

                #endregion



            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }





            return apiActionResult;


        }

        [Route("en-ur-drow-pass-rndom/{UrlName?}")]//--For security reason, just keep url not readable
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> EncryptPassword(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {

                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }

                }


                #region new
                string Password = String.Empty;
                if (requestParameters!=null && requestParameters.ContainsKey("Password"))
                {
                     Password = requestParameters["Password"].ToString() ?? "";
                     Password = CommonConversionHelper.Encrypt(Password);
                }
              
                Dictionary<string, string> responseDic= new Dictionary<string, string>();
                responseDic.Add("Password", Password);

                #region result
                result.Data = JsonConvert.SerializeObject(responseDic);
                result.StatusCode = 200;
                result.StatusMessage = "Ok";
                result.ErrorMessage = String.Empty;
                apiActionResult = new APIActionResult(result);
                #endregion


                #endregion



            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }





            return apiActionResult;


        }



        [Route("reset-password-by-phone/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> ResetPasswordByPhone(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {
                if (param != null && param.Count != 0)
                {
                    Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);
                        }
                    }

                    string? PhoneNumber = requestParameters != null ? requestParameters["PhoneNumber"].ToString() : "";
                    string? NewPassword = requestParameters != null ? requestParameters["NewPassword"].ToString() : "";
                    string? Email = requestParameters != null && requestParameters.ContainsKey("Email") ? requestParameters["Email"].ToString() : "";

                    if (String.IsNullOrEmpty(PhoneNumber))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please provide phone number!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    if (String.IsNullOrEmpty(NewPassword))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please provide new password!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    // Encrypt the password
                    string encryptedPassword = CommonConversionHelper.Encrypt(NewPassword);

                    // Reset password using phone number
                    string PasswordResetResponse = await this._userManagementServicesDAL.ResetUserPasswordByPhoneDAL(PhoneNumber, encryptedPassword);

                    if (PasswordResetResponse == "Saved Successfully!")
                    {
                        #region result
                        result.Data = "[]";
                        result.StatusCode = 200;
                        result.StatusMessage = "Ok";
                        result.Message = "Password reset successfully";
                        result.ErrorMessage = String.Empty;
                        apiActionResult = new APIActionResult(result);

                        #endregion
                    }
                    else
                    {
                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Failed to reset password. Please try again!";
                        apiActionResult = new APIActionResult(result);
                    }
                }
                else
                {
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "An error is occured while processing your request.";
                    apiActionResult = new APIActionResult(result);
                }
            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }

            return apiActionResult;
        }

        [Route("reset-password-firebase/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> ResetPasswordFirebase(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {
                if (param != null && param.Count != 0)
                {
                    Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);
                        }
                    }

                    string? Email = requestParameters != null && requestParameters.ContainsKey("Email") ? requestParameters["Email"].ToString() : "";
                    string? PhoneNumber = requestParameters != null && requestParameters.ContainsKey("PhoneNumber") ? requestParameters["PhoneNumber"].ToString() : "";
                    string? NewPassword = requestParameters != null && requestParameters.ContainsKey("NewPassword") ? requestParameters["NewPassword"].ToString() : "";
                    string? FirebaseUid = requestParameters != null && requestParameters.ContainsKey("FirebaseUid") ? requestParameters["FirebaseUid"].ToString() : "";

                    // Validate required fields
                    if (String.IsNullOrEmpty(Email) && String.IsNullOrEmpty(PhoneNumber))
                    {
                        result.StatusCode = 400;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please provide either email or phone number!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    if (String.IsNullOrEmpty(NewPassword))
                    {
                        result.StatusCode = 400;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please provide new password!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    if (String.IsNullOrEmpty(FirebaseUid))
                    {
                        result.StatusCode = 400;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Firebase authentication required!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    // Validate password strength
                    if (NewPassword.Length < 6)
                    {
                        result.StatusCode = 400;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Password must be at least 6 characters long!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    // This endpoint uses Firebase Authentication for password reset
                    // The actual password update is handled by Firebase Auth
                    // We just validate the request and return success
                    // The frontend should handle the Firebase password update

                    try
                    {
                        // Log the password reset attempt for audit purposes
                        string logMessage = $"Firebase password reset attempted for: {(!String.IsNullOrEmpty(Email) ? Email : PhoneNumber)}";
                        await this._commonServicesDAL.LogRunTimeExceptionDAL(logMessage, "Password Reset", "Firebase Auth");

                        // In a real implementation, you might want to:
                        // 1. Verify the Firebase UID is valid
                        // 2. Update any local user data if needed
                        // 3. Send confirmation email/SMS

                        #region result
                        result.Data = "[]";
                        result.StatusCode = 200;
                        result.StatusMessage = "Ok";
                        result.Message = "Password reset request processed successfully. Please complete the reset using Firebase Authentication.";
                        result.ErrorMessage = String.Empty;
                        apiActionResult = new APIActionResult(result);
                        #endregion
                    }
                    catch (Exception resetEx)
                    {
                        await this._commonServicesDAL.LogRunTimeExceptionDAL(resetEx.Message, resetEx.StackTrace, resetEx.StackTrace);
                        
                        result.StatusCode = 500;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "An error occurred while processing the password reset request.";
                        apiActionResult = new APIActionResult(result);
                    }
                }
                else
                {
                    result.StatusCode = 400;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "Invalid request parameters.";
                    apiActionResult = new APIActionResult(result);
                }
            }
            catch (Exception ex)
            {
                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 500;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error occurred while processing your request.";
                apiActionResult = new APIActionResult(result);
            }

            return apiActionResult;
        }

        [Route("download-digital-file/{order_item_id}/{user_id}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        [HttpGet]
        public async Task<IActionResult> DownloadDigitalFile(int order_item_id, int user_id)
        {
          

            try
            {
                var digitalOrderInfo = await this._salesServicesDAL.GetDigitalOrderInfoForCustomerByIdDAL(order_item_id, user_id);
                if (digitalOrderInfo!=null && digitalOrderInfo.IsDigitalProduct==true && !String.IsNullOrWhiteSpace(digitalOrderInfo.DigitalFileDownloadUrl))
                {

                    if (digitalOrderInfo.DigitalFileDownloadUrl.StartsWith("https://") || digitalOrderInfo.DigitalFileDownloadUrl.StartsWith("http://"))
                    {

                        string path = digitalOrderInfo.DigitalFileDownloadUrl;
                        string fileName = Path.GetFileName(path);
                        string fileExtension = Path.GetExtension(path);
                      
                        string contentType = await this._filesHelpers.GetFileContentTypeForFileExtension(fileExtension);


                        byte[]? fileBytes = null;
                        using (var httpClient = new HttpClient())
                        {
                            var response = await httpClient.GetAsync(path);
                            if (response.IsSuccessStatusCode)
                            {
                                fileBytes = await response.Content.ReadAsByteArrayAsync();
                            }
                            else
                            {
                                throw new Exception("Failed to download file.");
                            }
                        }

                        var fileStream = new MemoryStream(fileBytes);
                        

                        return File(fileStream, contentType ?? "application/octet-stream", fileName);



                    }
                    else
                    {
                        string path = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot" + digitalOrderInfo.DigitalFileDownloadUrl);
                        string fileName = Path.GetFileName(path);
                        string fileExtension = Path.GetExtension(path);
                        var file = System.IO.File.ReadAllBytes(path);
                        var fileStream = new MemoryStream(file);

                        string contentType = await this._filesHelpers.GetFileContentTypeForFileExtension(fileExtension);


                        return File(fileStream, contentType ?? "application/octet-stream", fileName);


                    }


                }
                else
                {

                    return StatusCode(StatusCodes.Status404NotFound);
                }

             


            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                return StatusCode(StatusCodes.Status500InternalServerError);
            }


        }


    }
}
