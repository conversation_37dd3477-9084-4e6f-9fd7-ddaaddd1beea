/*! jQuery-ui-Slider-Pips - v1.5.6 - 2014-07-04
* Copyright (c) 2014 <PERSON> <<EMAIL>>; Licensed  */
!function(a){"use strict";var b={pips:function(b){function c(b){var c=a(b).data("value"),d=a(e.element);if(!0===e.options.range){var f=d.slider("values");f[0]===f[1]?c<f[0]?d.slider("values",[c,f[1]]):d.slider("values",[f[0],c]):Math.abs(f[0]-c)===Math.abs(f[1]-c)?d.slider("values",[c,c]):Math.abs(f[0]-c)<Math.abs(f[1]-c)?d.slider("values",[c,f[1]]):d.slider("values",[f[0],c])}else d.slider("value",c)}function d(a){var b,c,d=a,i="ui-slider-pip",j="";"first"===a?d=0:"last"===a&&(d=g);var k=e.options.min+e.options.step*d,l=k.toString().replace(".","-");b=h.labels?h.labels[d]:k,"undefined"==typeof b&&(b=""),"first"===a?(c="0%",i+=" ui-slider-pip-first",i+="label"===h.first?" ui-slider-pip-label":"",i+=!1===h.first?" ui-slider-pip-hide":""):"last"===a?(c="100%",i+=" ui-slider-pip-last",i+="label"===h.last?" ui-slider-pip-label":"",i+=!1===h.last?" ui-slider-pip-hide":""):(c=(100/g*a).toFixed(4)+"%",i+="label"===h.rest?" ui-slider-pip-label":"",i+=!1===h.rest?" ui-slider-pip-hide":""),i+=" ui-slider-pip-"+l,j="horizontal"===e.options.orientation?"right: "+c:"bottom: "+c,f+='<span class="'+i+'" style="'+j+'"><span class="ui-slider-line"></span><span class="ui-slider-label" data-value="'+k+'">'+h.formatLabel(b)+"</span></span>"}var e=this,f="",g=(e.options.max-e.options.min)/e.options.step,h={first:"label",last:"label",rest:"pip",labels:!1,prefix:"",suffix:"",step:g>100?Math.floor(.05*g):1,formatLabel:function(a){return this.prefix+a+this.suffix}};a.extend(h,b),e.element.addClass("ui-slider-pips").find(".ui-slider-pip").remove(),h.step=Math.round(h.step),d("first");for(var i=1;g>i;i++)0===i%h.step&&d(i);d("last"),e.element.append(f),e.element.on("mousedown",".ui-slider-label",function(a){a.stopPropagation(),c(this)})}};a.extend(!0,a.ui.slider.prototype,b)}(jQuery),function(a){"use strict";var b={"float":function(b){var c,d,e=this,f=[],g={handle:!0,pips:!1,labels:!1,prefix:"",suffix:"",event:"slidechange slide",formatLabel:function(a){return this.prefix+a+this.suffix}};a.extend(g,b),e.options.value<e.options.min&&(e.options.value=e.options.min),e.options.value>e.options.max&&(e.options.value=e.options.max),e.options.values&&(e.options.values[0]<e.options.min&&(e.options.values[0]=e.options.min),e.options.values[1]<e.options.min&&(e.options.values[1]=e.options.min),e.options.values[0]>e.options.max&&(e.options.values[0]=e.options.max),e.options.values[1]>e.options.max&&(e.options.values[1]=e.options.max)),e.element.addClass("ui-slider-float").find(".ui-slider-tip, .ui-slider-tip-label").remove(),g.handle&&(e.options.values?(g.labels?(f[0]=g.labels[e.options.values[0]-e.options.min],f[1]=g.labels[e.options.values[1]-e.options.min],"undefined"==typeof f[0]&&(f[0]=e.options.values[0]),"undefined"==typeof f[1]&&(f[1]=e.options.values[1])):(f[0]=e.options.values[0],f[1]=e.options.values[1]),c=[a('<span class="ui-slider-tip">'+g.formatLabel(f[0])+"</span>"),a('<span class="ui-slider-tip">'+g.formatLabel(f[1])+"</span>")]):(g.labels?(d=g.labels[e.options.value-e.options.min],"undefined"==typeof d&&(d=e.options.value)):d=e.options.value,c=a('<span class="ui-slider-tip">'+g.formatLabel(d)+"</span>")),e.element.find(".ui-slider-handle").each(function(b,d){a(d).append(c[b])})),g.pips&&e.element.find(".ui-slider-label").each(function(b,c){var d=a(c).clone().removeClass("ui-slider-label").addClass("ui-slider-tip-label");d.insertAfter(a(c))}),"slide"!==g.event&&"slidechange"!==g.event&&"slide slidechange"!==g.event&&"slidechange slide"!==g.event&&(g.event="slidechange slide"),e.element.on(g.event,function(b,c){var d;g.labels?(d=g.labels[c.value-e.options.min],"undefined"==typeof d&&(d=c.value)):d=c.value,a(c.handle).find(".ui-slider-tip").html(g.formatLabel(d))})}};a.extend(!0,a.ui.slider.prototype,b)}(jQuery);