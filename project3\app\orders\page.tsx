'use client';

import { useState, useEffect, useCallback } from 'react';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useUser } from '@/contexts/user-context';

import { Config } from '@/lib/config';
import { Package, Search, ChevronRight, FileText, Truck, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { encryptOrderId, encryptValue } from '@/lib/encryption';

export default function OrdersPage() {
  const { t } = useSettings();
  const { user, isLoggedIn, isLoading, token } = useUser();
  const [activeTab, setActiveTab] = useState('all');
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [orderDetails, setOrderDetails] = useState<any[]>([]);
  const [loadingDetails, setLoadingDetails] = useState(false);

  // Define all useCallback hooks before any conditional returns
  const fetchOrderHistory = useCallback(async () => {
    setLoading(true);
    try {
      // Get JWT token from user context
      const authHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // Add JWT token to headers if available
      if (token) {
        authHeaders['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch('/api/orders/history', {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({
          requestParameters: {
            // UserId removed - will be auto-injected from JWT token
            recordValueJson: "[]"
          }
        })
      });

      const data = await response.json();
      console.log('Orders API response:', data);

      if (data && data.data) {
        let ordersData;
        
        // Check if data is a string that needs parsing (like login API) or already parsed
        if (typeof data.data === 'string') {
          ordersData = JSON.parse(data.data);
        } else {
          ordersData = data.data;
        }
        
        console.log('Parsed order data:', ordersData);
        
        if (Array.isArray(ordersData)) {
          console.log('First order structure:', ordersData[0]);
          
          // Map the API response to our expected format
          const mappedOrders = ordersData.map(order => ({
            id: order.OrderId,
            OrderID: order.OrderId,
            OrderNumber: order.OrderNumber,
            date: order.OrderDateUTC ? new Date(order.OrderDateUTC).toLocaleDateString() : 'N/A',
            OrderDate: order.OrderDateUTC,
            total: order.OrderTotal || 0,
            OrderTotal: order.OrderTotal || 0,
            TotalAmount: order.OrderTotal || 0,
            status: order.LatestStatusName || 'Active',
            StatusID: order.LatestStatusName === 'Active' ? 1 : 
                     order.LatestStatusName === 'In Progress' ? 2 :
                     order.LatestStatusName === 'Completed' ? 3 :
                     order.LatestStatusName === 'Returned' ? 4 :
                     order.LatestStatusName === 'Refunded' ? 5 :
                     order.LatestStatusName === 'Cancelled' ? 6 : 1,
            ItemCount: order.TotalItems || 0,
            items: order.OrderItems || [],
            OrderItems: order.OrderItems || []
          }));
          
          // Sort orders from newest to oldest
          const sortedOrders = mappedOrders.sort((a, b) => {
            return new Date(b.OrderDate || 0).getTime() - new Date(a.OrderDate || 0).getTime();
          });
          
          setOrders(sortedOrders);
          console.log('Mapped and sorted orders:', sortedOrders);
        } else {
          setOrders([]);
        }
      }
    } catch (error) {
      console.error('Error fetching order history:', error);
    } finally {
      setLoading(false);
    }
  }, [token]);

  const fetchOrderDetails = useCallback(async (orderId: number) => {
    setLoadingDetails(true);
    try {
      // Get JWT token from user context
      const authHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // Add JWT token to headers if available
      if (token) {
        authHeaders['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch('/api/orders/details', {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({
          requestParameters: {
            OrderId: orderId,
            // UserId removed - will be auto-injected from JWT token
            recordValueJson: "[]"
          }
        })
      });

      const data = await response.json();
      if (data && data.data) {
        const parsedData = JSON.parse(data.data);
        if (Array.isArray(parsedData)) {
          setOrderDetails(parsedData);
        }
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
    } finally {
      setLoadingDetails(false);
    }
  }, [token]);

  // Fetch order history
  useEffect(() => {
    const userId = user?.UserID || user?.UserId;
    if (isLoggedIn && userId) {
      fetchOrderHistory();
    }
  }, [isLoggedIn, user, fetchOrderHistory]);

  // Redirect to login if not authenticated (only after loading is complete)
  useEffect(() => {
    if (!isLoading && !isLoggedIn) {
      window.location.href = '/login?redirect=/orders';
    }
  }, [isLoading, isLoggedIn]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-lg text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Show login message if not authenticated (after loading is complete)
  if (!isLoggedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-muted-foreground">
            Redirecting to login...
          </p>
        </div>
      </div>
    );
  }

  // Mock orders data (fallback)
  const mockOrders = [
    {
      id: 'ORD-2023-1001',
      date: '2023-12-15',
      total: 129.99,
      StatusID: 3,
      items: [
        { name: 'Medical Terminology Book', ProductName: 'Medical Terminology Book', quantity: 1, price: 49.99 },
        { name: 'Anatomy Atlas', ProductName: 'Anatomy Atlas', quantity: 1, price: 79.99 }
      ]
    },
    {
      id: 'ORD-2023-0892',
      date: '2023-11-28',
      total: 199.99,
      StatusID: 2,
      items: [
        { name: 'Clinical Medicine Course', ProductName: 'Clinical Medicine Course', quantity: 1, price: 199.99 }
      ]
    },
    {
      id: 'ORD-2023-0765',
      date: '2023-10-05',
      total: 45.99,
      StatusID: 6,
      items: [
        { name: 'Pharmacology Flashcards', ProductName: 'Pharmacology Flashcards', quantity: 1, price: 45.99 }
      ]
    }
  ];

  // Filter orders based on active tab
  const displayOrders = loading ? [] : orders;
  const filteredOrders = activeTab === 'all' 
    ? displayOrders 
    : displayOrders.filter(order => {
        const statusId = order.StatusID || order.OrderStatusId || order.StateId || 1;
        return statusId.toString() === activeTab;
      });

  // Create secure query parameters for order details
  const createSecureOrderLink = (order: any) => {
    const orderId = order.OrderId || order.OrderID || order.id;
    const encryptedOrderId = encryptOrderId(orderId);
    
    // Encrypt sensitive data for query parameters
    const orderTotal = (order.total || order.OrderTotal || order.TotalAmount || 0).toFixed(2);
    const orderDate = (() => {
      const dateStr = order.OrderDateUTC || order.date || order.OrderDate || order.CreatedOn;
      if (dateStr) {
        try {
          const date = new Date(dateStr);
          return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });
        } catch (e) {
          return 'N/A';
        }
      }
      return 'N/A';
    })();
    
    // Encrypt the query parameters
    const encryptedTotal = encryptValue(orderTotal);
    const encryptedDate = encryptValue(orderDate);
    
    return `/orders/${encryptedOrderId}?t=${encryptedTotal}&d=${encryptedDate}`;
  };

  // Status mapping based on StatusID
  const getStatusInfo = (statusId: number | string) => {
    const id = parseInt(statusId?.toString() || '1');
    switch(id) {
      case 1:
        return { name: 'Active', icon: <Package className="h-5 w-5 text-blue-500" />, color: 'bg-blue-100 text-blue-800' };
      case 2:
        return { name: 'In Progress', icon: <Truck className="h-5 w-5 text-orange-500" />, color: 'bg-orange-100 text-orange-800' };
      case 3:
        return { name: 'Completed', icon: <CheckCircle className="h-5 w-5 text-green-500" />, color: 'bg-green-100 text-green-800' };
      case 4:
        return { name: 'Returned', icon: <RefreshCw className="h-5 w-5 text-purple-500" />, color: 'bg-purple-100 text-purple-800' };
      case 5:
        return { name: 'Refunded', icon: <RefreshCw className="h-5 w-5 text-indigo-500" />, color: 'bg-indigo-100 text-indigo-800' };
      case 6:
        return { name: 'Cancelled', icon: <AlertCircle className="h-5 w-5 text-red-500" />, color: 'bg-red-100 text-red-800' };
      default:
        return { name: 'Unknown', icon: <Package className="h-5 w-5 text-gray-500" />, color: 'bg-gray-100 text-gray-800' };
    }
  };

  // Show loading state if user is not authenticated or still loading
  if (!isLoggedIn || !user) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">{t('home')}</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{t('orders')}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold mb-6">{t('orders')}</h1>
          <Card className="p-8">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">Loading your orders...</p>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">{t('home')}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t('orders')}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Content */}
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">{t('orders')}</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchOrderHistory}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>
        
        {/* Order Tabs */}
        <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="all">All Orders</TabsTrigger>
            <TabsTrigger value="1">Active</TabsTrigger>
            <TabsTrigger value="2">In Progress</TabsTrigger>
            <TabsTrigger value="3">Completed</TabsTrigger>
            <TabsTrigger value="6">Cancelled</TabsTrigger>
          </TabsList>
        </Tabs>
        
        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input 
              type="text" 
              placeholder="Search orders..." 
              className="w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Sort by:</span>
            <select className="border border-input rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
              <option value="date-desc">Date (Newest)</option>
              <option value="date-asc">Date (Oldest)</option>
              <option value="total-desc">Amount (High to Low)</option>
              <option value="total-asc">Amount (Low to High)</option>
            </select>
          </div>
        </div>
        
        {/* Orders List */}
        {loading ? (
          <div className="space-y-4">
            {/* Loading Skeleton */}
            {[...Array(3)].map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <div className="border-b border-border p-4">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Skeleton className="h-5 w-32" />
                        <Skeleton className="h-6 w-20 rounded-full" />
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-16" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <Skeleton className="h-16 w-full rounded-md" />
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <Skeleton className="h-6 w-20" />
                      <Skeleton className="h-8 w-24" />
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : filteredOrders.length > 0 ? (
          <div className="space-y-4">
            {filteredOrders.map((order, orderIndex) => (
              <Card key={order.OrderId || order.OrderID || order.id || orderIndex} className="overflow-hidden">
                <div className="border-b border-border p-4">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium">
                          {order.OrderNumber || `OR#${String(order.OrderId || order.OrderID).padStart(8, '0')}`}
                        </h3>
                        {(() => {
                          // Use LatestStatusName from API if available, otherwise fall back to StatusID mapping
                          const statusName = order.LatestStatusName;
                          if (statusName) {
                            // Map API status names to our display format
                            let displayStatus = statusName;
                            let statusColor = 'bg-blue-100 text-blue-800';
                            let statusIcon = <Package className="h-5 w-5 text-blue-500" />;

                            switch (statusName.toLowerCase()) {
                              case 'active':
                                statusColor = 'bg-blue-100 text-blue-800';
                                statusIcon = <Package className="h-5 w-5 text-blue-500" />;
                                break;
                              case 'in progress':
                              case 'processing':
                                statusColor = 'bg-orange-100 text-orange-800';
                                statusIcon = <Truck className="h-5 w-5 text-orange-500" />;
                                break;
                              case 'completed':
                              case 'delivered':
                                statusColor = 'bg-green-100 text-green-800';
                                statusIcon = <CheckCircle className="h-5 w-5 text-green-500" />;
                                break;
                              case 'cancelled':
                                statusColor = 'bg-red-100 text-red-800';
                                statusIcon = <AlertCircle className="h-5 w-5 text-red-500" />;
                                break;
                              default:
                                statusColor = 'bg-gray-100 text-gray-800';
                                statusIcon = <Package className="h-5 w-5 text-gray-500" />;
                            }

                            return (
                              <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${statusColor}`}>
                                {statusIcon}
                                <span>{displayStatus}</span>
                              </div>
                            );
                          } else {
                            // Fallback to StatusID mapping
                            const statusId = order.StatusID || order.OrderStatusId || order.StateId || 1;
                            const statusInfo = getStatusInfo(statusId);
                            return (
                              <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${statusInfo.color}`}>
                                {statusInfo.icon}
                                <span>{statusInfo.name}</span>
                              </div>
                            );
                          }
                        })()}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        Ordered on {(() => {
                          const dateStr = order.OrderDateUTC || order.date || order.OrderDate || order.CreatedOn;
                          if (dateStr) {
                            try {
                              const date = new Date(dateStr);
                              return date.toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              });
                            } catch (e) {
                              return dateStr;
                            }
                          }
                          return 'N/A';
                        })()}
                      </p>
                      {/* Show main product names prominently */}
                      {(order.items || order.OrderItems || []).length > 0 && (
                        <div className="bg-blue-50 px-3 py-2 rounded-md">
                          <p className="text-sm font-medium text-blue-900 mb-1">Products in this order:</p>
                          <div className="text-sm text-blue-800 space-y-1">
                            {(order.items || order.OrderItems || [])
                              .slice(0, 3)
                              .map((item: any, index: number) => {
                                // Get product name from item data
                                const productName = item.name ||
                                                  item.ProductName ||
                                                  item.ItemName ||
                                                  'Medical Product';
                                const productId = item.ProductID || item.ProductId;

                                return (
                                  <span key={index}>
                                    {productId ? (
                                      <Link
                                        href={`/product/${productId}`}
                                        className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                      >
                                        {productName}
                                      </Link>
                                    ) : (
                                      <span className="font-medium">{productName}</span>
                                    )}
                                    {index < Math.min(2, (order.items || order.OrderItems || []).length - 1) && ' • '}
                                  </span>
                                );
                              })}
                            {(order.items || order.OrderItems || []).length > 3 && (
                              <span className="text-blue-600"> • +{(order.items || order.OrderItems || []).length - 3} more items</span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  
                    <div className="flex items-center gap-4 mt-4 md:mt-0">
                      <div className="text-right">
                        <p className="font-medium">
                          ${(order.total || order.OrderTotal || order.TotalAmount || 0).toFixed(2)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {order.TotalItems || (order.items?.length || order.OrderItems?.length || order.ItemCount || 0)} item(s)
                        </p>
                      </div>
                      
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex items-center gap-1"
                        asChild
                      >
                        <Link href={createSecureOrderLink(order)}>
                          <FileText className="h-4 w-4" />
                          Details
                          <ChevronRight className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
                
                {/* Show order items preview - limit to 3 items */}
                <div className="p-4 bg-muted/30">
                  <h4 className="text-sm font-medium mb-2">Order Items Preview</h4>
                  <div className="space-y-2">
                    {(order.items || order.OrderItems || []).slice(0, 3).map((item: any, index: number) => {
                      const productImage = item.ProductImageUrl || item.ImageUrl || item.ProductImage;

                      return (
                        <div key={index} className="flex justify-between items-center">
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-muted rounded-md overflow-hidden flex items-center justify-center">
                              {productImage ? (
                                <img
                                  src={productImage.startsWith('http') ? productImage : `${Config.ADMIN_BASE_URL}${productImage}`}
                                  alt={item.name || item.ProductName || 'Product'}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.style.display = 'none';
                                    const parent = target.parentElement;
                                    if (parent) {
                                      parent.innerHTML = '<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>';
                                    }
                                  }}
                                />
                              ) : (
                                <Package className="h-6 w-6 text-gray-400" />
                              )}
                            </div>
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">
                                {item.name || item.ProductName || item.ItemName || 'Medical Product'}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Qty: {item.quantity || item.Quantity || item.OrderQuantity || 1}
                              </p>
                              {item.ProductDescription && (
                                <p className="text-xs text-gray-500 mt-1 line-clamp-1">
                                  {item.ProductDescription}
                                </p>
                              )}
                            </div>
                          </div>
                          <p className="text-sm font-medium">
                            ${(item.price || item.Price || item.UnitPrice || item.ItemPrice || 0).toFixed(2)}
                          </p>
                        </div>
                      );
                    })}
                    {(order.items?.length || order.OrderItems?.length || 0) > 3 && (
                      <div className="text-xs text-muted-foreground text-center pt-2">
                        +{(order.items?.length || order.OrderItems?.length || 0) - 3} more items
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No orders found</h3>
              <p className="text-muted-foreground mb-4">You don't have any {activeTab !== 'all' ? activeTab : ''} orders yet.</p>
              <Button asChild>
                <Link href="/">
                  Continue Shopping
                </Link>
              </Button>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}