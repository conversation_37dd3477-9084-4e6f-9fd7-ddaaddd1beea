{"name": "code-medical-web", "version": "0.1.0", "private": true, "dependencies": {"@paypal/react-paypal-js": "^7.8.1", "@stripe/react-stripe-js": "^1.9.0", "@stripe/stripe-js": "^1.32.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^0.27.2", "body-parser": "^1.20.0 ", "express": "^4.18.1", "next": "^12.1.6", "next-images": "^1.8.4", "next-redux-wrapper": "^7.0.5", "react": "^18.1.0", "react-accessible-accordion": "^5.0.0", "react-dom": "^18.1.0", "react-helmet": "^6.1.0", "react-image-gallery": "^1.2.8", "react-image-lightbox": "^5.1.4", "react-js-pagination": "^3.0.3", "react-owl-carousel3": "^2.2.5", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-scripts": "^5.0.1", "react-slick": "^0.29.0", "react-toastify": "^9.0.5", "react-tooltip": "^4.2.21", "react-visibility-sensor": "^5.1.1", "reactstrap": "^9.0.3", "redux": "^4.2.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-state-sync": "^3.1.2", "redux-thunk": "^2.4.1", "sass": "^1.52.1", "strip-ansi": "^7.0.1", "stripe": "^9.5.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}