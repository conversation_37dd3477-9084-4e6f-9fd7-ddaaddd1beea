### The following convention is used for naming the translation files:

```
LANGUAGE_COUNTRY.js
```

#### Where:

```
LANGUAGE = The lowercase ISO 639-1 language code.
```

> See http://en.wikipedia.org/wiki/List_of_ISO_639-1_codes

```
COUNTRY = The uppercase ISO 3166-1 country code.
```

> See http://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements

When there is no `COUNTRY` in the filename, it is assumed the generic language is used in multiple countries.
