{"name": "code-medical-web", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "^3.6.9", "@paypal/react-paypal-js": "^7.8.1", "@stripe/react-stripe-js": "^1.9.0", "@stripe/stripe-js": "^1.32.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/react-slick": "^0.23.10", "@types/react-sticky-el": "^1.0.3", "@types/reactstrap": "^8.7.1", "animate.css": "^4.1.1", "axios": "^0.27.2", "bootstrap-scss": "^5.2.1", "deepmerge": "^4.2.2", "i18next": "^22.4.9", "i18next-browser-languagedetector": "^7.0.1", "lodash": "^4.17.21", "next": "^12.2.3", "photoswipe": "^5.3.2", "react": "^18.2.0", "react-content-loader": "^6.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.33.1", "react-i18next": "^11.8.15", "react-js-pagination": "^3.0.3", "react-masonry-css": "^1.0.16", "react-photoswipe-gallery": "^2.2.1", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-slick": "^0.29.0", "react-sticky-el": "^2.0.9", "react-toastify": "^9.0.5", "react-tooltip": "^4.2.21", "reactstrap": "^9.1.1", "redux": "^4.2.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-state-sync": "^3.1.2", "redux-thunk": "^2.4.1", "sass": "^1.53.0", "strip-ansi": "^7.0.1", "stripe": "^9.5.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}