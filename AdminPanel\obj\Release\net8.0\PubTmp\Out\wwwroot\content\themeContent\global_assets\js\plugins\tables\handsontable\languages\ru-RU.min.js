/*!
 * numbro.js language configuration
 * language : Russian
 * locale : Russsia
 * author : <PERSON><PERSON><PERSON> : https://github.com/a<PERSON><PERSON><PERSON><PERSON>
 */
(function(){"use strict";var a={langLocaleCode:"ru-RU",cultureCode:"ru-RU",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){
// not ideal, but since in Russian it can taken on
// different forms (masculine, feminine, neuter)
// this is all we can do
return"."},currency:{symbol:"руб.",position:"postfix"},defaults:{currencyFormat:",4 a"},formats:{fourDigits:"4 a",fullWithTwoDecimals:",0.00 $",fullWithTwoDecimalsNoCurrency:",0.00",fullWithNoDecimals:",0 $"}};
// CommonJS
"undefined"!=typeof module&&module.exports&&(module.exports=a),
// Browser
"undefined"!=typeof window&&window.numbro&&window.numbro.culture&&window.numbro.culture(a.cultureCode,a)}).call("undefined"==typeof window?this:window);