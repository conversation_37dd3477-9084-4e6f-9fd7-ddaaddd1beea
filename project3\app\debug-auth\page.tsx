'use client';

import { useUser } from '@/contexts/user-context';
import { useEffect, useState } from 'react';

export default function DebugAuthPage() {
  const { user, isLoggedIn, isLoading, token } = useUser();
  const [cookieData, setCookieData] = useState<any>(null);
  const [tokenData, setTokenData] = useState<any>(null);

  useEffect(() => {
    // Get cookie data (only on client side)
    if (typeof window !== 'undefined') {
      const cookies = document.cookie.split(';');
      let userData = null;
      
      for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'auth_user') {
          try {
            userData = JSON.parse(decodeURIComponent(value));
            break;
          } catch (e) {
            console.error("Error parsing user cookie:", e);
          }
        }
      }
      setCookieData(userData);
    }

    // Get token data
    fetch('/api/auth/get-token', {
      method: 'GET',
      credentials: 'include',
    })
    .then(res => res.json())
    .then(data => setTokenData(data))
    .catch(err => console.error('Token fetch error:', err));
  }, []);

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>
      
      <div className="space-y-6">
        {/* User Context State */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">User Context State</h2>
          <div className="space-y-2 text-sm">
            <p><strong>isLoading:</strong> {isLoading ? 'true' : 'false'}</p>
            <p><strong>isLoggedIn:</strong> {isLoggedIn ? 'true' : 'false'}</p>
            <p><strong>token exists:</strong> {token ? 'yes' : 'no'}</p>
            <p><strong>user exists:</strong> {user ? 'yes' : 'no'}</p>
            {user && (
              <div className="ml-4 space-y-1">
                <p><strong>UserId:</strong> {user.UserId}</p>
                <p><strong>UserID:</strong> {(user as any).UserID}</p>
                <p><strong>Email:</strong> {user.Email}</p>
                <p><strong>Name:</strong> {user.FirstName} {user.LastName}</p>
                <p><strong>Full user object:</strong></p>
                <pre className="bg-white p-2 rounded text-xs overflow-auto">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* Cookie Data */}
        <div className="bg-green-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Cookie Data</h2>
          <div className="text-sm">
            {cookieData ? (
              <div className="space-y-1">
                <p><strong>UserId:</strong> {cookieData.UserId}</p>
                <p><strong>UserID:</strong> {cookieData.UserID}</p>
                <p><strong>Email:</strong> {cookieData.Email}</p>
                <p><strong>Full cookie object:</strong></p>
                <pre className="bg-white p-2 rounded text-xs overflow-auto">
                  {JSON.stringify(cookieData, null, 2)}
                </pre>
              </div>
            ) : (
              <p>No cookie data found</p>
            )}
          </div>
        </div>

        {/* Token Data */}
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Token API Response</h2>
          <div className="text-sm">
            {tokenData ? (
              <pre className="bg-white p-2 rounded text-xs overflow-auto">
                {JSON.stringify(tokenData, null, 2)}
              </pre>
            ) : (
              <p>Loading token data...</p>
            )}
          </div>
        </div>

        {/* Raw Cookies */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Raw Cookies</h2>
          <div className="text-sm">
            <p className="break-all">
              {typeof window !== 'undefined' ? (document.cookie || 'No cookies found') : 'Loading cookies...'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}