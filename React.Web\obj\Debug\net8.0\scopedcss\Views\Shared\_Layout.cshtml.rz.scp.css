/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-fbswht42li] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-fbswht42li] {
  color: #0077cc;
}

.btn-primary[b-fbswht42li] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-fbswht42li], .nav-pills .show > .nav-link[b-fbswht42li] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-fbswht42li] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-fbswht42li] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-fbswht42li] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-fbswht42li] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-fbswht42li] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
