// German

jQuery.extend( jQuery.fn.pickadate.defaults, {
    monthsFull: [ '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'April', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'August', 'September', '<PERSON><PERSON><PERSON>', 'November', 'Dezember' ],
    monthsShort: [ '<PERSON>', 'Feb', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Okt', 'Nov', 'Dez' ],
    weekdaysFull: [ 'Sonntag', 'Mont<PERSON>', '<PERSON><PERSON><PERSON>', 'Mittwo<PERSON>', 'Don<PERSON>tag', 'Freitag', 'Samstag' ],
    weekdaysShort: [ 'So', '<PERSON>', 'Di', 'Mi', 'Do', 'Fr', 'Sa' ],
    today: 'Heute',
    clear: 'Lö<PERSON>',
    firstDay: 1,
    format: 'dddd, dd. mmmm yyyy',
    formatSubmit: 'yyyy/mm/dd'
});