'use client';

import Link from 'next/link';
import { ShoppingCart, Heart, Menu, X, Search, Flame, User, Home, Package, MapPin, FileText } from 'lucide-react';
import { useState } from 'react';
import { useSettings } from '@/contexts/settings-context';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useUser } from '@/contexts/user-context';
import { Button } from './button';

export function MainHeader() {
  const { t, primaryColor } = useSettings();
  const { totalItems } = useCart();
  const { totalItems: wishlistTotalItems } = useWishlist();
  const { user, isLoggedIn } = useUser();

  return (
    <header className="sticky top-0 z-50 w-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
      <div className="container mx-auto px-4 flex flex-col items-center justify-between gap-4 py-4">
        {/* Logo */}
        <Link href="/" className="flex-shrink-0">
          <div className="flex items-center">
            <img
              src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`}
              alt="Logo"
              className="h-12 w-auto bg-white p-2 rounded-md"
              onError={(e) => {
                console.error('Failed to load logo:', e.currentTarget.src);
                e.currentTarget.style.display = 'none';
              }}
            />
          </div>
        </Link>
        
        {/* Search Input - Moved below logo */}
        <div className="w-full max-w-lg">
          <div className="flex w-full">
            <input
              type="text"
              placeholder="Search"
              className="flex-1 p-2 border rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <button
              className="p-2 rounded-r-md text-white"
              style={{ backgroundColor: primaryColor }}
            >
              <Search className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Navigation Bar for Mobile */}
      <nav className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t md:hidden shadow-lg">
        <div className="flex justify-around items-center h-16">
          <Link href="/" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors">
            <Home className="h-6 w-6" />
            <span>{t('home')}</span>
          </Link>
          
          {isLoggedIn ? (
            <>
              <Link href="/account" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors">
                <User className="h-6 w-6" />
                <span>My Account</span>
              </Link>
              <Link href="/orders" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors">
                <FileText className="h-6 w-6" />
                <span>My Orders</span>
              </Link>
              <Link href="/addresses" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors">
                <MapPin className="h-6 w-6" />
                <span>My Addresses</span>
              </Link>
            </>
          ) : (
            <>
              <Link href="/hot-deals" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors">
                <Flame className="h-6 w-6" style={{ color: primaryColor }} />
                <span>{t('hotDeals')}</span>
              </Link>
              <Link href="/category" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors">
                <Package className="h-6 w-6" />
                <span>{t('categories')}</span>
              </Link>
              <Link href="/wishlist" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors relative">
                <Heart className="h-6 w-6" />
                {wishlistTotalItems > 0 && (
                  <span
                    className="absolute -top-1 -right-1 w-4 h-4 rounded-full text-[10px] flex items-center justify-center"
                    style={{ backgroundColor: primaryColor, color: 'white' }}
                  >
                    {wishlistTotalItems}
                  </span>
                )}
                <span>{t('wishlist')}</span>
              </Link>
            </>
          )}
          
          <Link href="/cart" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors relative">
            <ShoppingCart className="h-6 w-6" />
            {totalItems > 0 && (
              <span
                className="absolute -top-1 -right-1 w-4 h-4 rounded-full text-[10px] flex items-center justify-center"
                style={{ backgroundColor: primaryColor, color: 'white' }}
              >
                {totalItems}
              </span>
            )}
            <span>{t('cart')}</span>
          </Link>
          
          {!isLoggedIn && (
            <Link href="/login" className="flex flex-col items-center text-xs text-gray-700 hover:text-primary transition-colors">
              <User className="h-6 w-6" />
              <span>{t('login')}</span>
            </Link>
          )}
        </div>
      </nav>
    </header>
  );
}