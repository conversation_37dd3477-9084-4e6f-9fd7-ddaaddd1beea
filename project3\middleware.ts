import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define protected routes that require authentication
const protectedRoutes = [
  '/account',
  '/orders',
  '/profile',
  '/checkout',
  '/wishlist'
];

// Define public routes that should redirect authenticated users
const authRoutes = [
  '/login',
  '/signup'
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Get authentication cookies
  const authToken = request.cookies.get('auth_token') || request.cookies.get('auth_token_client');
  const userCookie = request.cookies.get('auth_user');

  // Check if user is authenticated
  const isAuthenticated = !!(authToken?.value && userCookie?.value);

  console.log('🔍 Middleware check:', {
    pathname,
    isAuthenticated,
    hasAuthToken: !!authToken?.value,
    tokenType: authToken?.name,
    hasUserCookie: !!userCookie?.value,
    allCookies: Object.fromEntries(request.cookies.getAll().map(c => [c.name, c.value?.substring(0, 10) + '...']))
  });

  // Handle protected routes
  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      console.log('🔒 Protected route accessed without authentication, redirecting to login');
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  // Handle auth routes (login, signup) - redirect if already authenticated
  if (authRoutes.some(route => pathname.startsWith(route))) {
    if (isAuthenticated) {
      console.log('🔓 Auth route accessed by authenticated user, redirecting');
      
      // Check if there's a redirect parameter
      const redirectParam = request.nextUrl.searchParams.get('redirect');
      if (redirectParam && redirectParam.startsWith('/') && !redirectParam.startsWith('//')) {
        console.log('🔄 Redirecting to stored URL:', redirectParam);
        return NextResponse.redirect(new URL(redirectParam, request.url));
      }
      
      // Default redirect for authenticated users
      return NextResponse.redirect(new URL('/account', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$|.*\\.ico$).*)',
  ],
};
